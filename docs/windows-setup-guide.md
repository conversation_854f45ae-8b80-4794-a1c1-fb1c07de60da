# Windows 環境安裝與設定指南

本指南將幫助您在 Windows 環境中設置完整的開發環境，包含必要的工具和服務。

## 基礎環境安裝

以下是在 Windows 系統上設置基礎開發環境的步驟：

- [ ] 安裝 Docker Desktop 並設定完成
- [ ] 安裝 kubectl 命令列工具
- [ ] 安裝 kind 工具
- [ ] 安裝 Helm
- [ ] 安裝 K9S 工具
- [ ] 安裝 Kubectx 和 Kubens 工具
- [ ] 安裝 Lens 工具



## 工具安裝

### 安裝 Docker Desktop 並設定完成
請自行完成 Docker Desktop 的安裝和設定，這邊不另作說明。

### 安裝 Kubectl 和 Kind

在 Windows 11 系統中，我們可以使用 PowerShell 來安裝 kubectl 和 kind。以下是詳細的安裝步驟：

#### 安裝 Kubectl

##### 方法一：使用 curl.exe（標準方法）

1. 以系統管理員身份開啟 PowerShell，執行以下命令來下載最新版本的 kubectl：

```powershell
curl.exe -LO "https://dl.k8s.io/release/$(curl.exe -L -s https://dl.k8s.io/release/stable.txt)/bin/windows/amd64/kubectl.exe"
```

##### 方法二：使用 Invoke-WebRequest（適用於沒有 curl.exe 的系統）

1. 以系統管理員身份開啟 PowerShell，執行以下命令：

```powershell
$version = (Invoke-WebRequest -URI "https://dl.k8s.io/release/stable.txt" -UseBasicParsing).Content
Invoke-WebRequest -URI "https://dl.k8s.io/release/$version/bin/windows/amd64/kubectl.exe" -OutFile "kubectl.exe"
```

##### 方法三：使用套件管理器

1. 使用 Chocolatey 安裝（需要先安裝 Chocolatey）：
```powershell
choco install kubernetes-cli
```

2. 使用 winget 安裝（Windows 11 內建）：
```powershell
winget install kubectl
```

##### 方法四：手動下載

1. 訪問 Kubernetes 官方發布頁面：https://kubernetes.io/releases/
2. 下載適用於 Windows amd64 的 kubectl.exe
3. 將下載的檔案重新命名為 kubectl.exe（如果需要）

#### 安裝後的共同步驟

2. 建立安裝目錄（如果尚未存在）：

```powershell
New-Item -ItemType Directory -Force -Path "C:\Program Files\Kubernetes"
```

3. 將 kubectl.exe 移動到安裝目錄：

```powershell
Move-Item -Force kubectl.exe "C:\Program Files\Kubernetes"
```

4. 將安裝目錄新增到系統的 PATH 環境變數：
   - 開啟「系統設定」（按 Windows 鍵 + X，選擇「系統」）
   - 點選「進階系統設定」
   - 點選「環境變數」
   - 在「系統變數」區塊中，找到並選擇「Path」
   - 點選「編輯」
   - 點選「新增」
   - 輸入 `C:\Program Files\Kubernetes`
   - 按「確定」儲存所有變更

5. 驗證安裝：
   - 開啟新的 PowerShell 視窗
   - 執行以下命令：
```powershell
kubectl version --client
```

#### 安裝 Kind

##### 方法一：使用 curl.exe（標準方法）

1. 以系統管理員身份開啟 PowerShell，執行以下命令來下載最新版本的 kind：

```powershell
curl.exe -Lo kind.exe https://kind.sigs.k8s.io/dl/v0.20.0/kind-windows-amd64
```

##### 方法二：使用 Invoke-WebRequest（適用於沒有 curl.exe 的系統）

1. 以系統管理員身份開啟 PowerShell，執行以下命令：

```powershell
Invoke-WebRequest -Uri "https://kind.sigs.k8s.io/dl/v0.20.0/kind-windows-amd64" -OutFile "kind.exe"
```

##### 方法三：使用套件管理器

1. 使用 Chocolatey 安裝（需要先安裝 Chocolatey）：
```powershell
choco install kind
```

2. 使用 winget 安裝（Windows 11 內建）：
```powershell
winget install kind
```

##### 方法四：手動下載

1. 訪問 Kind 官方發布頁面：https://github.com/kubernetes-sigs/kind/releases
2. 下載最新版本的 `kind-windows-amd64`
3. 將下載的檔案重新命名為 kind.exe

#### 安裝後的共同步驟

2. 將 kind.exe 移動到 Kubernetes 目錄（如果使用手動安裝方法）：

```powershell
Move-Item -Force kind.exe "C:\Program Files\Kubernetes"
```

3. 驗證安裝：
   - 開啟新的 PowerShell 視窗
   - 執行以下命令：
```powershell
kind version
```

### 安裝 Helm

#### 方法一：使用 curl.exe（標準方法）

1. 以系統管理員身份開啟 PowerShell，執行以下命令來下載最新版本的 Helm：

```powershell
curl.exe -Lo helm.zip "https://get.helm.sh/helm-v3.14.2-windows-amd64.zip"
Expand-Archive -Path helm.zip -DestinationPath .
```

#### 方法二：使用 Invoke-WebRequest（適用於沒有 curl.exe 的系統）

1. 以系統管理員身份開啟 PowerShell，執行以下命令：

```powershell
Invoke-WebRequest -Uri "https://get.helm.sh/helm-v3.14.2-windows-amd64.zip" -OutFile "helm.zip"
Expand-Archive -Path helm.zip -DestinationPath .
```

#### 方法三：使用套件管理器

1. 使用 Chocolatey 安裝（需要先安裝 Chocolatey）：
```powershell
choco install kubernetes-helm
```

2. 使用 winget 安裝（Windows 11 內建）：
```powershell
winget install Helm.Helm
```

#### 方法四：手動下載

1. 訪問 Helm 發布頁面：https://github.com/helm/helm/releases
2. 下載最新版本的 `helm-v3.X.X-windows-amd64.zip`
3. 解壓縮下載的檔案

#### 安裝後的共同步驟

1. 如果是使用方法一、二或四安裝，需要將解壓縮後的 helm.exe 移動到 Kubernetes 目錄：

```powershell
Move-Item -Force windows-amd64\helm.exe "C:\Program Files\Kubernetes"
Remove-Item -Force helm.zip
Remove-Item -Force -Recurse windows-amd64
```

2. 驗證安裝：
   - 開啟新的 PowerShell 視窗
   - 執行以下命令：
```powershell
helm version
```

3. 初始化 Helm（可選）：
```powershell
helm repo add bitnami https://charts.bitnami.com/bitnami
helm repo update
```

### 安裝 K9s

K9s 是一個終端機型的 Kubernetes 叢集管理工具。

#### 方法一：使用 curl.exe（標準方法）

1. 以系統管理員身份開啟 PowerShell，執行以下命令來下載最新版本的 K9s：

```powershell
curl.exe -Lo k9s.zip "https://github.com/derailed/k9s/releases/latest/download/k9s_Windows_amd64.zip"
Expand-Archive -Path k9s.zip -DestinationPath .
```

#### 方法二：使用 Invoke-WebRequest（適用於沒有 curl.exe 的系統）

1. 以系統管理員身份開啟 PowerShell，執行以下命令：

```powershell
$version = (Invoke-WebRequest -Uri "https://api.github.com/repos/derailed/k9s/releases/latest" -UseBasicParsing).Content | ConvertFrom-Json | Select-Object -ExpandProperty tag_name
Invoke-WebRequest -Uri "https://github.com/derailed/k9s/releases/download/$version/k9s_Windows_amd64.zip" -OutFile "k9s.zip"
Expand-Archive -Path k9s.zip -DestinationPath .
```

#### 方法三：使用套件管理器

1. 使用 Chocolatey 安裝（需要先安裝 Chocolatey）：
```powershell
choco install k9s
```

2. 使用 winget 安裝（Windows 11 內建）：
```powershell
winget install derailed.k9s
```

#### 方法四：手動下載

1. 訪問 K9s 發布頁面：https://github.com/derailed/k9s/releases
2. 下載最新版本的 `k9s_Windows_amd64.zip`
3. 解壓縮下載的檔案

#### 安裝後的共同步驟

1. 如果是使用方法一、二或四安裝，需要將解壓縮後的 k9s.exe 移動到 Kubernetes 目錄：

```powershell
Move-Item -Force k9s.exe "C:\Program Files\Kubernetes"
Remove-Item -Force k9s.zip
Remove-Item -Force -Recurse LICENSE README.md
```

2. 驗證安裝：
   - 開啟新的 PowerShell 視窗
   - 執行以下命令：
```powershell
k9s version
```

### 安裝 Kubectx 和 Kubens

Kubectx 用於管理和切換 Kubernetes 上下文，而 Kubens 用於切換命名空間。

#### 方法一：使用 curl.exe（標準方法）

1. 以系統管理員身份開啟 PowerShell，執行以下命令來下載最新版本：

```powershell
curl.exe -Lo kubectx.zip "https://github.com/ahmetb/kubectx/releases/latest/download/kubectx_v0.9.5_windows_x86_64.zip"
Expand-Archive -Path kubectx.zip -DestinationPath .
```

#### 方法二：使用 Invoke-WebRequest（適用於沒有 curl.exe 的系統）

1. 以系統管理員身份開啟 PowerShell，執行以下命令：

```powershell
$version = (Invoke-WebRequest -Uri "https://api.github.com/repos/ahmetb/kubectx/releases/latest" -UseBasicParsing).Content | ConvertFrom-Json | Select-Object -ExpandProperty tag_name
Invoke-WebRequest -Uri "https://github.com/ahmetb/kubectx/releases/download/$version/kubectx_${version}_windows_x86_64.zip" -OutFile "kubectx.zip"
Expand-Archive -Path kubectx.zip -DestinationPath .
```

#### 方法三：使用套件管理器

1. 使用 Chocolatey 安裝（需要先安裝 Chocolatey）：
```powershell
choco install kubectx
```

2. 使用 winget 安裝（Windows 11 內建）：
```powershell
winget install kubectx
```

#### 方法四：手動下載

1. 訪問 Kubectx 發布頁面：https://github.com/ahmetb/kubectx/releases
2. 下載最新版本的 Windows 二進位檔案
3. 解壓縮下載的檔案

#### 安裝後的共同步驟

1. 如果是使用方法一、二或四安裝，需要將解壓縮後的執行檔移動到 Kubernetes 目錄：

```powershell
Move-Item -Force kubectx.exe "C:\Program Files\Kubernetes"
Move-Item -Force kubens.exe "C:\Program Files\Kubernetes"
Remove-Item -Force kubectx.zip
```

2. 驗證安裝：
   - 開啟新的 PowerShell 視窗
   - 執行以下命令：
```powershell
kubectx --version
kubens --version
```

### 安裝 Lens

Lens 是一個功能強大的 Kubernetes IDE 和圖形化管理界面，可讓您以視覺化方式管理 Kubernetes 叢集。

1. 前往 [lens 官方發布頁面](https://k8slens.dev/)
2. 下載最新版本的 Windows 安裝檔（.exe 或 .msi）
3. 執行下載的安裝檔並依照安裝程序的提示進行操作

#### 安裝後的驗證

1. 安裝完成後，從開始功能表或應用程式列表中啟動 Lens
2. 當您第一次啟動 Lens 時，它會要求您添加一個叢集
3. 您可以通過將其指向您的 Kubernetes 配置文件（通常位於 `%USERPROFILE%\.kube\config`）來添加叢集

#### Lens 使用注意事項

- 確保 Kubernetes 叢集已經建立並正常運行
- 使用 Lens 時，可能需要同時開啟 Docker Desktop
- 傳輸層加密（TLS）可能需要額外設定，特別是在使用自簽證時
