# macOS 安裝指南

本文檔將指導您如何在 macOS 上通過 Homebrew 安裝相關會使用到的工具。這些工具對於在本地開發和測試 Kubernetes 環境是必不可少的。

## 基礎環境安裝

以下是在 MacOS 系統上設置基礎開發環境的步驟：

- [ ] 安裝 Docker Desktop 並設定完成
- [ ] 安裝 kind 工具
- [ ] 安裝 kubectl 命令列工具
- [ ] 安裝 Helm
- [ ] 安裝 k9s 工具
- [ ] 安裝 Kubectx 和 Kubens 工具
- [ ] 安裝 Lens 工具

## 前置需求

- macOS 作業系統
- [Homebrew](https://brew.sh/) 套件管理器
  - 如果尚未安裝 Homebrew，可以執行以下命令安裝：
    ```bash
    /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
    ```

## 安裝步驟

### 2. 安裝 Kind (Kubernetes IN Docker)

執行以下命令安裝 Kind：

```bash
brew install kind
```

### 3. 安裝 kubectl

kubectl 是用於與 Kubernetes 集群進行互動的命令列工具：

```bash
brew install kubectl
```

### 4. 安裝 Helm

Helm 是 Kubernetes 的套件管理器：

```bash
brew install helm
```

### 5. 安裝 k9s

k9s 是一個終端 UI，用於與 Kubernetes 叢集互動，提供直觀的介面來管理和監控資源：

```bash
brew install derailed/k9s/k9s
```

或者直接使用：

```bash
brew install k9s
```

### 6. 安裝 kubectx 和 kubens

kubectx 和 kubens 是兩個實用的命令列工具，可幫助您快速切換 Kubernetes 的環境與命名空間：

- **kubectx**：快速切換 Kubernetes 的環境與叢集
- **kubens**：快速切換 Kubernetes 的命名空間

```bash
brew install kubectx
```

安裝後，您將可以從命令列快速切換環境與命名空間，而不需要使用較長的 kubectl 命令。

### 7. 安裝 Lens

Lens 是一個功能強大的 Kubernetes IDE 和圖形化管理界面，可讓您以視覺化方式管理 Kubernetes 叢集：

使用 GUI 安裝方式如下：

1. 前往 [Lens 官方網站](https://k8slens.dev/) 下載最新的 macOS 版本
2. 將下載的檔案移動到應用程式資料夾
3. 雙擊啟動 Lens 應用程式

安裝完成後，您可以通過 Lens 的圖形界面輕鬆管理您的 Kubernetes 叢集、查看資源、檢視日誌以及調試應用程式。

## 驗證安裝

安裝完成後，您可以通過以下命令驗證各個工具是否安裝成功：

### 驗證 Kind
```bash
kind version
```

### 驗證 kubectl
```bash
kubectl version --client
```

### 驗證 Helm
```bash
helm version
```

### 驗證 k9s
```bash
k9s version
```

### 驗證 kubectx 和 kubens
```bash
kubectx --version
kubens --version
```

### 驗證 Lens

啟動 Lens 應用程式並確認它能夠正常運行。當您第一次啟動 Lens 時，它會要求您添加一個叢集。您可以通過將其指向您的 Kubernetes 配置文件（通常位於 `~/.kube/config`）來添加叢集。
