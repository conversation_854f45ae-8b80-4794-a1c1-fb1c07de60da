kind: Cluster
apiVersion: kind.x-k8s.io/v1alpha4
networking:
  apiServerAddress: "127.0.0.1"
  apiServerPort: 39711
nodes:
  - role: worker
    extraMounts:
      - hostPath: /Users/<USER>/kind
        containerPath: /mnt/kind
  - role: control-plane
    extraMounts:
      - hostPath: /Users/<USER>/kind
        containerPath: /mnt/kind
    kubeadmConfigPatches:
      - |
        kind: InitConfiguration
        nodeRegistration:
          kubeletExtraArgs:
            node-labels: "ingress-ready=true"
    extraPortMappings:
      - containerPort: 80
        hostPort: 8080
        listenAddress: "127.0.0.1"
        protocol: TCP
      - containerPort: 443
        hostPort: 8443
        listenAddress: "127.0.0.1"
        protocol: TCP
        # OpenTelemetry OTLP/gRPC
      - containerPort: 4317
        hostPort: 4317
        listenAddress: "127.0.0.1"
        protocol: TCP
        # OpenTelemetry OTLP/HTTP
      - containerPort: 4318
        hostPort: 4318
        listenAddress: "127.0.0.1"
        protocol: TCP
        # Web test faro port
      - containerPort: 8027
        hostPort: 8027
        listenAddress: "127.0.0.1"
        protocol: TCP
        # Nexus UI port
      - containerPort: 8081
        hostPort: 8081
        listenAddress: "127.0.0.1"
        protocol: TCP
        # Nexus Image port
      - containerPort: 8082
        hostPort: 8082
        listenAddress: "127.0.0.1"
        protocol: TCP
        # Node Port
      - containerPort: 30300
        hostPort: 30300
        listenAddress: "127.0.0.1"
        protocol: TCP
      - containerPort: 30301
        hostPort: 30301
        listenAddress: "127.0.0.1"
        protocol: TCP
      - containerPort: 30302
        hostPort: 30302
        listenAddress: "127.0.0.1"
        protocol: TCP
      - containerPort: 30303
        hostPort: 30303
        listenAddress: "127.0.0.1"
        protocol: TCP
      - containerPort: 30304
        hostPort: 30304
        listenAddress: "127.0.0.1"
        protocol: TCP
      - containerPort: 30305
        hostPort: 30305
        listenAddress: "127.0.0.1"
        protocol: TCP
      - containerPort: 30310
        hostPort: 30310
        listenAddress: "127.0.0.1"
        protocol: TCP
      - containerPort: 30317
        hostPort: 30317
        listenAddress: "127.0.0.1"
        protocol: TCP
      - containerPort: 30318
        hostPort: 30318
        listenAddress: "127.0.0.1"
        protocol: TCP
      - containerPort: 30327
        hostPort: 30327
        listenAddress: "127.0.0.1"
        protocol: TCP
