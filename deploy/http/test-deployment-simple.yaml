apiVersion: apps/v1
kind: Deployment
metadata:
  name: curl-test-deployment
  labels:
    app: curl-test
spec:
  replicas: 1
  selector:
    matchLabels:
      app: curl-test
  template:
    metadata:
      labels:
        app: curl-test
    spec:
      containers:
      - name: curl-container
        # 使用更小的映像，且 kind 通常都有這個映像
        image: busybox:latest
        command: ["/bin/sh"]
        args: ["-c", "while true; do echo 'Ready for commands...'; sleep 60; done"]
