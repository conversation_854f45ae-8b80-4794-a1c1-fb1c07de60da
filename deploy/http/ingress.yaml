apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: demo-api-ingress
  namespace: lab
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  ingressClassName: nginx
  rules:
  - host: demo.lab.dev  # 您可以修改為您希望的主機名
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: demo-api-service  # 這裡引用了我們剛剛創建的 service
            port:
              number: 80
