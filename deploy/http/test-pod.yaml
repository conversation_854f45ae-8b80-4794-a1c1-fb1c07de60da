apiVersion: apps/v1
kind: Deployment
metadata:
  name: curl-test-deployment
  labels:
    app: curl-test
spec:
  replicas: 1
  selector:
    matchLabels:
      app: curl-test
  template:
    metadata:
      labels:
        app: curl-test
    spec:
      containers:
      - name: curl-container
        image: curlimages/curl:latest
        command: ["/bin/sh"]
        args: ["-c", "while true; do echo 'Ready for curl commands...'; sleep 60; done"]
