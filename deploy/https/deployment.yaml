apiVersion: apps/v1
kind: Deployment
metadata:
  name: demo-api
  namespace: lab
spec:
  replicas: 1
  selector:
    matchLabels:
      app: demo-api
  template:
    metadata:
      labels:
        app: demo-api
    spec:
      containers:
      - name: demo-container
        image: ghcr.io/jamisliao/lab/demo:0.0.0.2
        ports:
        - containerPort: 8080
        resources:
          requests:
            memory: "64Mi"
            cpu: "100m"
          limits:
            memory: "128Mi"
            cpu: "200m"
