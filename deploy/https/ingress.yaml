apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: demo-api-ingress
  namespace: lab
  # annotations:
    # 可選：強制 HTTP 重定向到 HTTPS
    # nginx.ingress.kubernetes.io/ssl-redirect: "true"
    # 可選：設定 SSL 協議
    # nginx.ingress.kubernetes.io/ssl-protocols: "TLSv1.2 TLSv1.3"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - '*.lab.dev'
    secretName: lab-tls-secret
  rules:
  - host: demo.lab.dev
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: demo-api-service
            port:
              number: 80