global:
  domain: argocd.lab.dev

# 啟用憑證驗證
configs:
  createSecret: true
  secret:
    createSecret: true

server:
  # 啟用 ArgoCD Server
  ingress:
    # 啟用 Ingress
    enabled: true
    # 設定 hostname
    hostname: argocd.lab.dev
    # 啟用 TLS
    tls: true
    # Ingress 註解
    annotations:
      kubernetes.io/ingress.class: nginx
      nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
      nginx.ingress.kubernetes.io/backend-protocol: "HTTPS"
    # 不需要自動建立 TLS 憑證，使用已有的
    extraTls:
      - hosts:
          - argocd.lab.dev
        secretName: lab-tls-secret

  # 配置 ArgoCD Server 服務
  service:
    type: ClusterIP

# 重要的控制器設定
controller:
  replicas: 1

# Dex 配置 (用於 SSO)
dex:
  enabled: false
