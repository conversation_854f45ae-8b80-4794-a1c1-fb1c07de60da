apiVersion: apps/v1
kind: Deployment
metadata:
  name: demo-api
  labels:
    environment: share
spec:
  replicas: 1
  selector:
    matchLabels:
      app: demo-api
  template:
    metadata:
      labels:
        app: demo-api
      annotations:
        vault.hashicorp.com/agent-inject: "true"
        vault.hashicorp.com/agent-init-first: "true"
        vault.hashicorp.com/agent-pre-populate-only: "true"
        vault.hashicorp.com/agent-inject-status: "update"
        vault.hashicorp.com/role: "vault-sa"
        vault.hashicorp.com/agent-inject-secret-demo-creds.txt: "kv/data/share/demo"
        vault.hashicorp.com/agent-inject-template-demo-creds.txt: |
          {{- with secret "kv/data/share/demo" -}}
          username: {{ .Data.data.username }}
          password: {{ .Data.data.password }}
          {{ end }}
    spec:
      serviceAccountName: default
      containers:
      - name: demo-container
        image: ghcr.io/jamisliao/lab/demo:0.0.0.2
        command: ["/bin/sh", "-c"]
        args:
        - |
          # 等待 Vault 注入的機密文件被創建
          WAIT_SECONDS=0
          MAX_WAIT=30
          
          while [ ! -f /vault/secrets/demo-creds.txt ] && [ $WAIT_SECONDS -lt $MAX_WAIT ]; do
            echo "等待 Vault 機密文件... ($WAIT_SECONDS/$MAX_WAIT 秒)"
            sleep 2
            WAIT_SECONDS=$((WAIT_SECONDS+2))
          done
          
          if [ ! -f /vault/secrets/demo-creds.txt ]; then
            echo "等待超時：無法獲取 Vault 機密文件"
            exit 1
          fi
          
          # 從 Vault 機密文件讀取並設置環境變數
          echo "找到機密文件，設置環境變數..."
          
          # 解析機密並設置環境變數
          VAULT_USERNAME=$(grep "username:" /vault/secrets/demo-creds.txt | sed 's/username: //')
          VAULT_PASSWORD=$(grep "password:" /vault/secrets/demo-creds.txt | sed 's/password: //')
          
          # 導出環境變數
          export VAULT_USERNAME
          export VAULT_PASSWORD
          
          echo "已設置環境變數 VAULT_USERNAME 和 VAULT_PASSWORD"
          env
          # 執行應用程序
          echo "啟動主應用程序..."
          exec "$@"
          dotnet demo-api.dll
        env:
        - name: ENVIRONMENT
          value: "share"
        ports:
        - containerPort: 8080
        resources:
          limits:
            cpu: 200m
            memory: 128Mi
          requests:
            cpu: 100m
            memory: 64Mi