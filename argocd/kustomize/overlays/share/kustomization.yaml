apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
- ../../base

namespace: share

patches:
- path: deployment-patch.yaml
- path: service-patch.yaml

# JSON 6902 Patches 範例
patchesJson6902:
# 範例1: 修改 Deployment 的 replicas 數量
- target:
    group: apps        # API 群組
    version: v1        # API 版本
    kind: Deployment   # 資源類型
    name: demo-api     # 資源名稱
  patch: |-
    - op: replace
      path: /spec/replicas
      value: 2

# 範例2: 添加環境變數到容器
- target:
    group: apps        # API 群組
    version: v1        # API 版本
    kind: Deployment   # 資源類型
    name: demo-api     # 資源名稱
  patch: |-
    - op: add
      path: /spec/template/spec/containers/0/env
      value:
        - name: ENVIRONMENT
          value: share
        - name: LOG_LEVEL
          value: info

# 範例3: 修改資源限制
- target:
    group: apps
    version: v1
    kind: Deployment
    name: demo-api
  patch: |-
    - op: replace
      path: /spec/template/spec/containers/0/resources/limits/memory
      value: 256Mi
    - op: replace
      path: /spec/template/spec/containers/0/resources/requests/memory
      value: 128Mi

# 範例4: 添加標籤
- target:
    group: apps
    version: v1
    kind: Deployment
    name: demo-api
  patch: |-
    - op: add
      path: /metadata/labels/team
      value: platform
    - op: add
      path: /spec/template/metadata/labels/version
      value: v1.0.0

images:
- name: ghcr.io/jamisliao/lab/demo
  newTag: 0.0.0.2
