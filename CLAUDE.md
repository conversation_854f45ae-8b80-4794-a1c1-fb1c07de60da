# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

This is a Kubernetes learning lab focused on teaching Kind (Kubernetes in Docker) concepts, GitOps with ArgoCD, observability with Grafana stack, and secrets management with Vault. The repository contains practical examples and configurations for local Kubernetes development.

## Core Technologies

- **Kind**: Local Kubernetes cluster using Docker containers
- **ArgoCD**: GitOps continuous delivery tool
- **Kustomize**: Kubernetes configuration management without templates
- **Grafana Stack**: Complete observability (Prometheus, Loki, Tempo, Mimir, Alloy)
- **Vault**: Secrets management with Kubernetes integration
- **.NET Core**: Demo API application for testing deployments

## Essential Commands

### Kind Cluster Management
```bash
# Create cluster with custom configuration
kind create cluster -n jamis-lab --image kindest/node:v1.32.2 --config ./kind.yaml

# Delete cluster
kind delete cluster -n jamis-lab
```

### Kubernetes Development
```bash
# Build and push demo application
cd demo/src
docker build -t ghcr.io/{username}/lab/demo:0.0.0.0 -f ./demo-api/Dockerfile .
docker push ghcr.io/{username}/lab/demo:0.0.0.0

# Deploy applications using Kustomize
kubectl apply -k argocd/kustomize/overlays/share/
kubectl apply -k argocd/kustomize/overlays/sharewithvault/

# Test deployments
kubectl get pods -n lab
kubectl logs -f deployment/demo-api -n lab
```

### Observability Stack
```bash
# Install Grafana monitoring stack
kubectl create namespace monitoring
kubens monitoring
helm repo add grafana https://grafana.github.io/helm-charts
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo update

# Deploy components
helm install prometheus prometheus-community/kube-prometheus-stack -f opentelemetry/custom-prometheus.yaml
helm install loki grafana/loki-stack -f opentelemetry/custom-loki.yaml
helm install mimir grafana/mimir-distributed -f opentelemetry/custom-mimir.yaml
helm install tempo grafana/tempo -f opentelemetry/tempo/custom-tempo.yaml
helm install alloy grafana/alloy -f opentelemetry/alloy/custom-alloy.yaml

# Verify deployments
kubectl get pods | grep -E 'loki|mimir|tempo|prometheus|grafana'
./opentelemetry/check-environment.sh
```

### ArgoCD GitOps
```bash
# Install ArgoCD
kubectl create namespace argocd
kubens argocd
helm repo add argo https://argoproj.github.io/argo-helm
helm install argocd argo/argo-cd --namespace argocd -f argocd/helm/values.yaml

# Get admin password
kubectl -n argocd get secret argocd-initial-admin-secret -o jsonpath="{.data.password}" | base64 -d

# Access ArgoCD UI at https://argocd.lab.dev:8443
```

### Vault Secrets Management
```bash
# Install Vault
kubectl create namespace resource
kubens resource
helm install vault hashicorp/vault -f vault/helm/values.yaml

# Initialize and configure Vault
kubectl exec -it vault-0 -- vault operator init
kubectl exec -it vault-0 -- vault operator unseal <key1>
kubectl exec -it vault-0 -- vault operator unseal <key2>
kubectl exec -it vault-0 -- vault operator unseal <key3>

# Setup Kubernetes auth and policies
kubectl exec -it vault-0 -- vault auth enable kubernetes
kubectl exec -it vault-0 -- vault secrets enable -version=2 kv
```

### Kustomize Validation and Deployment
```bash
# Validate all Kustomize examples
cd option_content/kubtomize
./validate.sh

# Deploy specific examples
./deploy.sh comprehensive-dev apply
./deploy.sh team-skilltree build
./deploy.sh patch-json validate
```

### Certificate Management
```bash
# Generate self-signed certificates for HTTPS
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout cert/tls.key -out cert/tls.crt \
  -subj "/CN=*.lab.dev" \
  -addext "subjectAltName=DNS:*.lab.dev"

# Create Kubernetes TLS secret
kubectl create secret tls lab-tls-secret \
  --key cert/tls.key \
  --cert cert/tls.crt \
  --namespace lab
```

## Architecture Overview

### Kind Cluster Configuration
- **Control Plane**: Single node with ingress-ready label
- **Worker Node**: Single node for workloads
- **Port Mappings**: HTTP (8080), HTTPS (8443), OTLP (4317, 4318), NodePorts (30300-30327)
- **Volume Mounts**: `/Users/<USER>/kind` mounted to `/mnt/kind` in containers

### Network Flow
```
External Request → Host Ports (8080/8443) → Kind Container Ports (80/443) → 
Ingress Controller → Services → Pods
```

### Namespace Organization
- `default`: Basic workloads and testing
- `lab`: Main application deployments
- `monitoring`: Grafana observability stack
- `resource`: Vault and infrastructure services
- `argocd`: GitOps management
- `local-path-storage`: Storage provisioner

### GitOps Workflow
1. Configuration changes pushed to Git
2. ArgoCD detects changes and syncs
3. Kustomize builds environment-specific manifests
4. Resources deployed to appropriate namespaces
5. Monitoring tracks deployment health

## Development Patterns

### Kustomize Structure
- **Base**: Common resource definitions in `base/` directories
- **Overlays**: Environment-specific patches in `overlays/`
- **Teams**: Multi-tenant configurations with namespace isolation
- **Components**: Reusable modules for complex compositions

### Vault Integration Patterns
- **Agent Injector**: Automatic secret injection into pods via annotations
- **Secrets Operator**: Sync Vault secrets to Kubernetes Secret resources
- **Dynamic Secrets**: Generate temporary credentials for services

### Observability Patterns
- **Metrics**: Prometheus/Mimir for time-series data
- **Logs**: Loki for log aggregation and analysis
- **Traces**: Tempo for distributed tracing
- **Alloy**: Unified telemetry collection and routing

## Important Files and Directories

### Configuration
- `kind.yaml`: Kind cluster configuration with port mappings
- `kind-ingress-nginx.yaml`: Ingress controller for Kind
- `argocd/helm/values.yaml`: ArgoCD Helm configuration
- `vault/helm/values.yaml`: Vault Helm configuration

### Deployments
- `deploy/http/`: Basic HTTP service deployments
- `deploy/https/`: HTTPS-enabled service deployments
- `argocd/kustomize/`: GitOps application definitions

### Demo Application
- `demo/src/demo-api/`: .NET Core weather API
- `demo/src/demo-api/Dockerfile`: Container build definition
- `demo/src/demo-api/demo-api.csproj`: .NET project file

### Monitoring Configurations
- `opentelemetry/custom-*.yaml`: Custom Helm values for observability stack
- `opentelemetry/alloy/`: Grafana Alloy telemetry configuration
- `opentelemetry/check-environment.sh`: Environment validation script

### Learning Examples
- `option_content/kubtomize/`: Comprehensive Kustomize learning examples
- `option_content/kubtomize/validate.sh`: Validation script for examples
- `option_content/kubtomize/deploy.sh`: Deployment script for examples

## Common Troubleshooting

### Port Conflicts
Check if required ports are available before creating Kind cluster:
```bash
netstat -an | grep -E "(8080|8443|4317|4318)"
```

### DNS Resolution
Add to `/etc/hosts` (macOS) or `C:\Windows\System32\drivers\etc\hosts` (Windows):
```
127.0.0.1 demo.lab.dev
127.0.0.1 grafana.lab.dev
127.0.0.1 argocd.lab.dev
127.0.0.1 alloy.lab.dev
```

### Certificate Issues
Use `-k` flag with curl for self-signed certificates:
```bash
curl -k https://demo.lab.dev:8443/weatherforecast
```

### Vault Unsealing
Always unseal Vault after pod restarts:
```bash
kubectl exec -it vault-0 -- vault operator unseal <key>
```

## Platform-Specific Notes

### macOS Setup
- Install tools via Homebrew: `brew install kind kubectl helm`
- Docker Desktop required for Kind
- Use `brew install kubecolor kubectx k9s` for enhanced CLI experience

### Windows Setup
- Use PowerShell or Windows Terminal
- Install tools via winget, chocolatey, or manual download
- Windows paths in documentation use `C:\Program Files\Kubernetes`

### Host Path Configuration
Update `kind.yaml` with appropriate host paths:
- macOS: `/Users/<USER>/kind`
- Windows: `C:\Users\<USER>\kind`
- Linux: `/home/<USER>/kind`

## Environment Variables

When working with this codebase, ensure these are set appropriately:
- `KUBECONFIG`: Path to kubectl configuration
- `DOCKER_HOST`: Docker daemon connection (if not default)
- GitHub credentials for container registry access

This repository serves as a comprehensive learning environment for modern Kubernetes development practices, emphasizing GitOps, observability, and security best practices.