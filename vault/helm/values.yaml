global:
  # 設定部署的命名空間
  namespace: "resource"
  # 禁用 TLS（按照要求）
  tlsDisable: true

server:
  # 開啟 Vault 伺服器
  enabled: true
  
  # 設定 Ingress 以便訪問 Vault
  ingress:
    enabled: true
    annotations:
      kubernetes.io/ingress.class: nginx
    pathType: Prefix
    hosts:
      - host: vault.lab.dev
        paths: 
          - /
    tls:
      - secretName: lab-tls-secret
        hosts:
          - vault.lab.dev
  
  # 資料存儲配置
  dataStorage:
    enabled: true
    size: 10Gi
    mountPath: "/vault/data"
    # 使用標準存儲類別
    storageClass: standard
    accessMode: ReadWriteOnce
  
  # 開啟 Kubernetes 認證
  authDelegator:
    enabled: true
  # 設定服務帳號
  serviceAccount:
    create: true
    name: "vault-auth"
  
  # 使用 standalone 模式
  standalone:
    enabled: true
    config: |-
      ui = true

      listener "tcp" {
        tls_disable = 1
        address = "[::]:8200"
        cluster_address = "[::]:8201"
      }
      storage "file" {
        path = "/vault/data"
      }

# 開啟 UI 介面
ui:
  enabled: true
  serviceType: "ClusterIP"