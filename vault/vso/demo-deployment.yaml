
apiVersion: apps/v1
kind: Deployment
metadata:
  name: demo-vault-operator
  namespace: sharewithvault
spec:
  replicas: 1
  selector:
    matchLabels:
      app: demo-vault-operator
  template:
    metadata:
      labels:
        app: demo-vault-operator
    spec:
      containers:
      - name: demo-container
        image: ghcr.io/jamisliao/lab/demo:0.0.0.2
        ports:
        - containerPort: 8080
        resources:
          limits:
            cpu: 200m
            memory: 128Mi
          requests:
            cpu: 100m
            memory: 64Mi
        env:
        - name: VAULT_USERNAME
          valueFrom:
            secretKeyRef:
              name: share-demo
              key: username
        - name: VAULT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: share-demo
              key: password
