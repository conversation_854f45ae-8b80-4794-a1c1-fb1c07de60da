#!/bin/bash
# 步驟1：環境準備檢查腳本
# check-environment.sh

echo "=== Grafana Agent Metrics 故障排除指南 ==="

# 1. 檢查 namespace 是否存在
echo -e "\n1. 檢查 monitoring namespace..."
kubectl get namespace monitoring
if [ $? -ne 0 ]; then
    echo "✗ monitoring namespace 不存在，正在創建..."
    kubectl create namespace monitoring
else
    echo "✓ monitoring namespace 存在"
fi

# 2. 檢查 mimir 相關服務
echo -e "\n2. 檢查 mimir 服務狀態..."
kubectl get svc -n monitoring | grep -i mimir
if [ $? -ne 0 ]; then
    echo "⚠️  未找到 mimir 服務，需要先部署 mimir"
    echo "請確認 mimir-nginx 服務是否正確部署"
else
    echo "✓ 找到 mimir 相關服務"
fi

# 3. 檢查現有的 grafana-agent
echo -e "\n3. 檢查現有的 grafana-agent..."
kubectl get configmap grafana-agent-integrated -n monitoring
kubectl get pod grafana-agent-integrated -n monitoring

# 4. 檢查網路連通性
echo -e "\n4. 檢查集群內部 DNS 解析..."
kubectl run dns-test --image=busybox:1.28 --rm -it --restart=Never -- \
  nslookup mimir-nginx.monitoring.svc.cluster.local 2>/dev/null
if [ $? -eq 0 ]; then
    echo "✓ DNS 解析正常"
else
    echo "⚠️  DNS 解析可能有問題"
fi

echo -e "\n=== 環境檢查完成 ==="