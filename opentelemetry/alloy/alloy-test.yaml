apiVersion: v1
kind: ConfigMap
metadata:
  name: python-metric-sender-script-alloy-test 
  labels:
    app: otel-alloy-test-python-grpc
data:
  send_metric_alloy_test.py: | 
    import os
    import time
    import random
    import logging
    import sys
    
    print("🚀 Starting OTEL Continuous Sender...")
    print(f"Python version: {sys.version}")
    
    # 導入 OpenTelemetry 模組
    print("📦 Importing OpenTelemetry modules...")
    from opentelemetry import metrics, trace
    from opentelemetry.sdk.metrics import MeterProvider
    from opentelemetry.sdk.metrics.export import PeriodicExportingMetricReader
    from opentelemetry.sdk.trace import TracerProvider
    from opentelemetry.sdk.trace.export import BatchSpanProcessor
    from opentelemetry.sdk._logs import LoggerProvider, LoggingHandler
    from opentelemetry.sdk._logs.export import BatchLogRecordProcessor
    from opentelemetry.exporter.otlp.proto.grpc.metric_exporter import OTLPMetricExporter
    from opentelemetry.exporter.otlp.proto.grpc.trace_exporter import OTLPSpanExporter
    from opentelemetry.exporter.otlp.proto.grpc._log_exporter import OTLPLogExporter
    from opentelemetry.sdk.resources import Resource, SERVICE_NAME
    print("✅ All modules imported successfully")
    
    # 配置
    endpoint = os.getenv("OTEL_EXPORTER_OTLP_ENDPOINT", "http://my-alloy-external.monitoring.svc.cluster.local:4317")
    pod_name = os.getenv("HOSTNAME", "unknown_pod")
    pod_namespace = os.getenv("POD_NAMESPACE", "default")
    service_name = "python-otel-continuous-sender"
    
    print(f"🎯 Target: {endpoint}")
    print(f"🏷️  Service: {service_name}")
    print(f"🏠 Pod: {pod_name} (Namespace: {pod_namespace})")
    
    # 創建共享資源
    resource = Resource(attributes={
        SERVICE_NAME: service_name,
        "k8s.pod.name": pod_name,
        "k8s.namespace.name": pod_namespace,
        "application": "otel-continuous-test",
        "version": "1.0.0"
    })
    print("✅ Resource created")
    
    try:
        # === METRICS 設定 ===
        print("🔢 Setting up Metrics...")
        metrics_exporter = OTLPMetricExporter(endpoint=endpoint, timeout=10, insecure=True)
        metrics_reader = PeriodicExportingMetricReader(metrics_exporter, export_interval_millis=5000)
        meter_provider = MeterProvider(resource=resource, metric_readers=[metrics_reader])
        metrics.set_meter_provider(meter_provider)
        meter = metrics.get_meter("continuous.sender.meter")
        
        # 創建指標
        request_counter = meter.create_counter(
            name="otel_continuous_requests_total",
            description="Total requests sent continuously",
            unit="1"
        )
        response_time_histogram = meter.create_histogram(
            name="otel_continuous_response_time",
            description="Response time histogram",
            unit="ms"
        )
        active_operations_gauge = meter.create_up_down_counter(
            name="otel_continuous_active_operations",
            description="Number of active operations",
            unit="1"
        )
        print("✅ Metrics setup complete")
        
        # === TRACES 設定 ===
        print("🔍 Setting up Traces...")
        trace_exporter = OTLPSpanExporter(endpoint=endpoint, timeout=10, insecure=True)
        span_processor = BatchSpanProcessor(trace_exporter)
        tracer_provider = TracerProvider(resource=resource)
        tracer_provider.add_span_processor(span_processor)
        trace.set_tracer_provider(tracer_provider)
        tracer = trace.get_tracer("continuous.sender.tracer")
        print("✅ Traces setup complete")
        
        # === LOGS 設定 ===
        print("📝 Setting up Logs...")
        log_exporter = OTLPLogExporter(endpoint=endpoint, timeout=10, insecure=True)
        log_processor = BatchLogRecordProcessor(log_exporter)
        logger_provider = LoggerProvider(resource=resource)
        logger_provider.add_log_record_processor(log_processor)
        
        # 配置 Python logging
        handler = LoggingHandler(level=logging.NOTSET, logger_provider=logger_provider)
        logging.getLogger().addHandler(handler)
        logging.getLogger().setLevel(logging.INFO)
        print("✅ Logs setup complete")
        
        print("\n🎉 All telemetry providers initialized successfully!")
        print("🔄 Starting continuous loop (every 5 seconds)...")
        print("💡 Press Ctrl+C to stop\n")
        
        # === 持續發送迴圈 ===
        loop_count = 0
        start_time = time.time()
        active_ops = 0
        
        while True:
            loop_count += 1
            iteration_start = time.time()
            
            # 生成隨機的模擬資料
            operations = ["get_user", "create_order", "update_profile", "delete_item", "search_products"]
            operation_name = random.choice(operations)
            status_codes = ["200", "201", "400", "404", "500"]
            status_code = random.choice(status_codes)
            response_time = random.uniform(10, 500)  # 10-500ms
            
            # 更新活躍操作數
            active_ops += 1
            active_operations_gauge.add(1)
            
            # 建立 Trace Span
            with tracer.start_as_current_span(f"{operation_name}-{loop_count}") as span:
                try:
                    # 設定 span 屬性
                    span.set_attribute("http.method", random.choice(["GET", "POST", "PUT", "DELETE"]))
                    span.set_attribute("http.status_code", int(status_code))
                    span.set_attribute("operation.name", operation_name)
                    span.set_attribute("loop.iteration", loop_count)
                    span.set_attribute("user.id", random.randint(1000, 9999))
                    span.set_attribute("service.version", "1.0.0")
                    
                    # 添加事件
                    span.add_event("Operation started", {
                        "timestamp": time.time(),
                        "thread": "main",
                        "request_id": f"req-{loop_count}"
                    })
                    
                    # 模擬處理時間
                    time.sleep(response_time / 1000)
                    
                    # 記錄 Metrics
                    request_counter.add(1, {
                        "status_code": status_code,
                        "operation": operation_name,
                        "environment": "continuous_test",
                        "method": span.get_span_context().span_id  # 使用 span ID 作為標籤
                    })
                    
                    response_time_histogram.record(response_time, {
                        "status_code": status_code,
                        "operation": operation_name,
                        "success": "true" if status_code.startswith("2") else "false"
                    })
                    
                    # 設定 span 屬性和狀態
                    span.set_attribute("response.time_ms", response_time)
                    span.set_attribute("response.size_bytes", random.randint(100, 5000))
                    
                    if status_code.startswith("5"):
                        span.set_status(trace.Status(trace.StatusCode.ERROR, f"Server error: {status_code}"))
                        span.record_exception(Exception(f"Simulated server error {status_code}"))
                        error_type = "server_error"
                    elif status_code.startswith("4"):
                        span.set_status(trace.Status(trace.StatusCode.ERROR, f"Client error: {status_code}"))
                        error_type = "client_error"
                    else:
                        span.set_status(trace.Status(trace.StatusCode.OK))
                        error_type = None
                    
                    # 添加完成事件
                    span.add_event("Operation completed", {
                        "result": "success" if status_code.startswith("2") else "error",
                        "final_status": status_code,
                        "processing_time_ms": response_time
                    })
                    
                    # 發送結構化日誌
                    log_extra = {
                        "loop_count": loop_count,
                        "operation": operation_name,
                        "status_code": status_code,
                        "response_time_ms": response_time,
                        "span_id": format(span.get_span_context().span_id, '016x'),
                        "trace_id": format(span.get_span_context().trace_id, '032x'),
                        "user_id": span.get_span_context().span_id % 10000,  # 模擬用戶 ID
                    }
                    
                    if status_code.startswith("2"):
                        logging.info(
                            f"Operation {operation_name} completed successfully",
                            extra={**log_extra, "result": "success"}
                        )
                    elif status_code.startswith("4"):
                        logging.warning(
                            f"Operation {operation_name} failed with client error {status_code}",
                            extra={**log_extra, "result": "client_error", "error_type": error_type}
                        )
                    else:
                        logging.error(
                            f"Operation {operation_name} failed with server error {status_code}",
                            extra={**log_extra, "result": "server_error", "error_type": error_type}
                        )
                    
                finally:
                    # 減少活躍操作數
                    active_ops -= 1
                    active_operations_gauge.add(-1)
            
            # 計算時間統計
            iteration_end = time.time()
            iteration_duration = iteration_end - iteration_start
            total_runtime = iteration_end - start_time
            
            # 顯示進度（每 10 次顯示詳細資訊）
            if loop_count % 10 == 0:
                avg_time = total_runtime / loop_count
                print(f"📊 Stats after {loop_count} iterations:")
                print(f"   ⏱️  Total runtime: {total_runtime:.1f}s")
                print(f"   📈 Average time per loop: {avg_time:.2f}s")
                print(f"   🔄 Current active operations: {active_ops}")
            else:
                # 簡單的進度指示
                status_emoji = "✅" if status_code.startswith("2") else "⚠️" if status_code.startswith("4") else "❌"
                print(f"{status_emoji} Loop {loop_count}: {operation_name} [{status_code}] ({response_time:.1f}ms)")
            
            # 等待下次執行（確保間隔為 5 秒）
            sleep_time = max(0, 5.0 - iteration_duration)
            if sleep_time > 0:
                time.sleep(sleep_time)
                
    except KeyboardInterrupt:
        print("\n🛑 Received interrupt signal, shutting down gracefully...")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        print("\n🧹 Cleaning up telemetry providers...")
        try:
            if 'meter_provider' in locals():
                meter_provider.shutdown()
                print("✅ Metrics provider shut down")
        except Exception as e:
            print(f"❌ Error shutting down metrics: {e}")
            
        try:
            if 'tracer_provider' in locals():
                tracer_provider.shutdown()
                print("✅ Traces provider shut down")
        except Exception as e:
            print(f"❌ Error shutting down traces: {e}")
            
        try:
            if 'logger_provider' in locals():
                logger_provider.shutdown()
                print("✅ Logs provider shut down")
        except Exception as e:
            print(f"❌ Error shutting down logs: {e}")
            
        print(f"🏁 OTEL Continuous Sender terminated after {loop_count} iterations")

---
apiVersion: v1
kind: Pod
metadata:
  name: otel-alloy-test-python-grpc-pod 
  labels:
    app: otel-alloy-test-python-grpc
spec:
  restartPolicy: Never 
  volumes:
    - name: python-script-vol-alloy-test
      configMap:
        name: python-metric-sender-script-alloy-test 
        items:
          - key: send_metric_alloy_test.py 
            path: send_metric_alloy_test.py 
            mode: 0755

  containers:
    - name: python-alloy-grpc-sender
      image: python:3.9-slim-bookworm
      env:
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: OTEL_EXPORTER_OTLP_ENDPOINT
          value: "http://my-alloy-external.monitoring.svc.cluster.local:4317"
      command: ["/bin/sh", "-c"]
      args:
        - |
          set -e
          echo "🚀 Starting OTEL continuous sender container..."
          echo "📦 Installing OpenTelemetry dependencies..."
          pip install --no-cache-dir --disable-pip-version-check --quiet \
            opentelemetry-api==1.25.0 \
            opentelemetry-sdk==1.25.0 \
            opentelemetry-exporter-otlp-proto-grpc==1.25.0
          echo "✅ Dependencies installed successfully"
          echo "🔄 Starting continuous OTEL sender (runs indefinitely)..."
          python -u /app/send_metric_alloy_test.py
      volumeMounts:
        - name: python-script-vol-alloy-test
          mountPath: /app 
          readOnly: true
      resources:
        limits:
          memory: "256Mi"
          cpu: "500m"
        requests:
          memory: "128Mi"
          cpu: "100m"
