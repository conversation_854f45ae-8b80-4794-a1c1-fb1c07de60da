apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    nginx.ingress.kubernetes.io/backend-protocol: HTTP
  name: alloy-ingress
  namespace: monitoring
spec:
  ingressClassName: nginx
  rules:
  - host: alloy.lab.dev
    http:
      paths:
        - pathType: ImplementationSpecific
          path: /
          backend:
            service:
              name: alloy-cluster
              port:
                number: 12345
  tls:
  - hosts:
    - alloy.lab.dev
    secretName: lab-tls-secret