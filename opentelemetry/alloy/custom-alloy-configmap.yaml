apiVersion: v1
kind: ConfigMap
metadata:
  name: alloy-config
  namespace: monitor
data:
  config.alloy: |
    prometheus.scrape "argo_workflow_metrics" {
      targets = [
        {"__address__" = "workflow-controller-metrics.argo.svc.cluster.local:9090"},
      ]

      forward_to = [prometheus.remote_write.mimir.receiver]
      scrape_interval = "60s"
      scrape_timeout = "10s"
    }

    prometheus.exporter.self "example" {}

    logging {
      level = "info"
      format = "logfmt"
    }

    loki.write "loki_output" {
        endpoint {
            url = "http://loki:3100/loki/api/v1/push"
        }
    }

    loki.process "process_logs" {
      forward_to = [loki.write.loki_output.receiver]
      stage.logfmt {
          mapping = { "kind" = "", "service_name" = "", "app" = "" }
      }

      stage.labels {
          values = { "kind" = "kind", "service_name" = "service_name", "app" = "app" }
      }
    }

    loki.source.api "loki_push_api" {
        http {
            listen_address = "0.0.0.0"
            listen_port = 3100
        }
        forward_to = [
            loki.process.process_logs.receiver,
        ]
        labels = {
            forwarded = "true",
        }
    }

    livedebugging {
      enabled = true
    }

    faro.receiver "integrations_app_agent_receiver" {

      extra_log_labels = {
        app  = "Demo-APP",
        kind = "",
        source = "faro",
      }
      server {
        listen_address           = "0.0.0.0"
        listen_port              = 8027
        cors_allowed_origins     = ["*"]
        max_allowed_payload_size = "10MiB"

        rate_limiting {
          rate = 50
        }
      }

      sourcemaps { }

      output {
        logs   = [loki.process.process_logs.receiver]
        traces = [otelcol.exporter.otlp.trace_write.input]
      }
    }

    otelcol.receiver.prometheus "demo" {
      output {
        metrics = [otelcol.exporter.prometheus.default.input]
      }
    }

    otelcol.receiver.otlp "default" {
      http {
      }
      grpc {
        include_metadata = true
      }

      output {
        metrics = [otelcol.processor.batch.default.input]
        logs    = [otelcol.processor.batch.default.input]
        traces  = [otelcol.processor.batch.default.input]
      }
    }

    otelcol.processor.batch "default" {
      output {
        metrics = [otelcol.exporter.prometheus.default.input]
        logs = [otelcol.exporter.loki.default.input]
        traces = [otelcol.exporter.otlp.trace_write.input]
      }
    }

    otelcol.exporter.prometheus "default" {
      forward_to = [prometheus.remote_write.mimir.receiver]
    }

    otelcol.exporter.loki "default" {
      forward_to = [loki.write.loki_output.receiver]
    }


    otelcol.exporter.otlp "trace_write" {
      retry_on_failure {
        max_elapsed_time = "1m0s"
      }

      client {
        endpoint = "tempo.monitor:4317"
        tls {
          insecure = true
        }
      }
    }

    prometheus.remote_write "mimir" {
      endpoint {
        url = "http://mimir-nginx:80/api/v1/push"
      }
    }
