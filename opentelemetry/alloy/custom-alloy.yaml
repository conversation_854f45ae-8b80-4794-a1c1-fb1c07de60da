alloy:
  configMap:
    create: true  # true，讓 Helm 創建 ConfigMap
    content: |  # 包含實際的配置內容
      logging {
        level = "info"
        format = "logfmt"
      }

      livedebugging {
        enabled = true
      }

      loki.write "loki_output" {
          endpoint {
              url = "http://loki:3100/loki/api/v1/push"
          }
      }

      loki.process "process_logs" {
        forward_to = [loki.write.loki_output.receiver]
        stage.logfmt {
            mapping = { "kind" = "", "service_name" = "", "app" = "" }
        }

        stage.labels {
            values = { "kind" = "kind", "service_name" = "service_name", "app" = "app" }
        }
      }

      loki.source.api "loki_push_api" {
          http {
              listen_address = "0.0.0.0"
              listen_port = 3100
          }
          forward_to = [
              loki.process.process_logs.receiver,
          ]
          labels = {
              forwarded = "true",
          }
      }

      faro.receiver "integrations_app_agent_receiver" {
        extra_log_labels = {
          app  = "Demo-APP",
          kind = "",
          source = "faro",
        }
        server {
          listen_address           = "0.0.0.0"
          listen_port              = 8027
          cors_allowed_origins     = ["*"]
          max_allowed_payload_size = "10MiB"

          rate_limiting {
            rate = 50
          }
        }

        sourcemaps { }

        output {
          logs   = [loki.process.process_logs.receiver]
          traces = [otelcol.exporter.otlp.trace_write.input]
        }
      }

      otelcol.receiver.otlp "default" {
        http {
        }
        grpc {
          include_metadata = true
        }

        output {
          metrics = [otelcol.processor.batch.default.input]
          logs    = [otelcol.processor.batch.default.input]
          traces  = [otelcol.processor.batch.default.input]
        }
      }

      otelcol.processor.batch "default" {
        output {
          metrics = [otelcol.exporter.prometheus.default.input]
          logs = [otelcol.exporter.loki.default.input]
          traces = [otelcol.exporter.otlp.trace_write.input]
        }
      }

      otelcol.exporter.prometheus "default" {
        forward_to = [prometheus.remote_write.prometheus.receiver]
      }

      otelcol.exporter.loki "default" {
        forward_to = [loki.write.loki_output.receiver]
      }

      otelcol.exporter.otlp "trace_write" {
        retry_on_failure {
          max_elapsed_time = "1m0s"
        }

        client {
          endpoint = "tempo:4317"
          tls {
            insecure = true
          }
        }
      }

      // 修改：將 metrics 寫到 Prometheus 的 remote write receiver
      prometheus.remote_write "prometheus" {
        endpoint {
          url = "http://prometheus-kube-prometheus-prometheus:9090/api/v1/write"  // 使用正確的 Prometheus service 名稱

          // 可選：如果 Prometheus 需要認證
          // basic_auth {
          //   username = "your_username"
          //   password = "your_password"
          // }

          // 可選：設定寫入超時
          // remote_timeout = "30s"

          // 可選：設定重試參數
          // queue_config {
          //   capacity = 2500
          //   max_samples_per_send = 500
          //   batch_send_deadline = "5s"
          // }
        }
      }

  stabilityLevel: "experimental"

  # 將原本在 agent 區塊的端口配置移到這裡，並指定 NodePort
  extraPorts:
    - name: "faro"
      port: 8027
      targetPort: 8027
      protocol: "TCP"
      nodePort: 30827  # 指定 Faro 的 NodePort
    - name: "prometheus"
      port: 9090
      targetPort: 9090
      protocol: "TCP"
      nodePort: 30090  # 指定 Prometheus 的 NodePort
    - port: 4317
      targetPort: 4317
      protocol: TCP
      name: otlp-grpc
      appProtocol: grpc
      nodePort: 30317  # 指定 otlp-grpc 的 NodePort
    - port: 4318
      targetPort: 4318
      protocol: TCP
      name: otlp-http
      nodePort: 30318  # 指定 otlp-http 的 NodePort

  livedebugging:
    enabled: true

  clustering:
    enabled: true

controller:
  type: 'statefulset'
  replicas: 3

service:
  # 將原本 agent.type 的 NodePort 設定移到這裡
  type: NodePort
  enabled: true

  # 指定主要服務的 NodePort (通常是 Alloy 管理界面的端口)
  nodePort: 31128
