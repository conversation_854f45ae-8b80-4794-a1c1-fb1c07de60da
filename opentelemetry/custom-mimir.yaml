# Persistence for local storage (since we are using filesystem backend)
mimir:
  zoneAwareReplication:
    enabled: false  # 單節點環境不需要 zone-aware replication
    migration:
      enabled: false   # 單節點環境不需要 zone-aware replication
  structuredConfig:
    multitenancy_enabled: false
    limits:
      ingestion_burst_size: 100000
      ingestion_rate: 50000
      max_label_names_per_series: 120
      out_of_order_time_window: 5m
      compactor_blocks_retention_period: 14d  # 刪除超過 14 days 的數據
    common:
      storage:
        backend: filesystem
        filesystem:
          dir: /var/mimir/data

  minio:
    enabled: false  # 使用 filesystem backend 時不需要 MinIO

  # 設定組件使用 mimir-pvc
  ingester:
    replicas: 1  # 降低複製數，讓它變成單節點
    zoneAwareReplication:
      enabled: false  # 明確禁用 zone-aware replication
      migration:
        enabled: false  # 明確禁用 migration
    resources:
      limits:
        memory: 2Gi
      requests:
        cpu: 100m
        memory: 128Mi
    persistence:
      enabled: true
      storageClass: standard  # 統一使用 storageClass
      size: 5Gi

  distributor:
    replicas: 1
    resources:
      limits:
        memory: 2Gi
      requests:
        cpu: 100m
        memory: 128Mi
  query_frontend:
    replicas: 1
  querier:
    replicas: 1

  store_gateway:
    replicas: 1
    persistence:
      enabled: true
      storageClass: standard

  compactor:
    replicas: 1
    persistentVolume:
      enabled: true
      storageClass: standard
      size: 2Gi
