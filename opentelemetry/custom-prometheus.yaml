prometheus:
  prometheusSpec:
    enableFeatures:
      - exemplar-storage
    enableRemoteWriteReceiver: true
    serviceMonitorSelectorNilUsesHelmValues: false
    retention: 10d  #保留 10 天的指標數據
    # 大小保留限制
    retentionSize: "" 
    # TSDB 設定
    tsdb:
      outOfOrderTimeWindow: 0s  #不允許亂序數據
    # WAL 壓縮
    walCompression: true  #啟用 WAL 壓縮
    # Prometheus 儲存設定 - 使用 standard StorageClass
    storageSpec:
      volumeClaimTemplate:
        spec:
          storageClassName: standard
          accessModes: ["ReadWriteOnce"]
          resources:
            requests:
              storage: 50Gi
  service:
    nodePort: 30305

grafana:
  # Grafana 持久性儲存設定
  persistence:
    enabled: true
    storageClassName: standard  # 指定使用 standard StorageClass
    accessModes:
      - ReadWriteOnce
    size: 10Gi
    # 可選：指定 PVC 的標籤和註解
    annotations: {}
    labels: {}
  
  # 管理員帳號設定
  adminUser: admin
  adminPassword: admin

  # Ingress 設定
  ingress:
    enabled: true
    ingressClassName: nginx
    tls:
    - secretName: lab-tls-secret
      hosts:
      - grafana.lab.dev
  
  # 資源限制
  resources:
    requests:
      memory: 256Mi
      cpu: 100m
    limits:
      memory: 512Mi
      cpu: 200m

  # 環境變數設定 - 改善數據源健康檢查
  env:
    # 設定數據源默認 HTTP 超時
    GF_DATASOURCES_DEFAULT_HTTP_TIMEOUT: 30s
    # 設定默認 HTTP 方法
    GF_DATASOURCES_DEFAULT_HTTP_METHOD: GET
    # 設定默認健康檢查路徑
    GF_DATASOURCES_DEFAULT_HEALTH_CHECK_PATH: /ready

  # 啟用 sidecar 自動發現數據源
  sidecar:
    datasources:
      enabled: true
      label: grafana_datasource
      labelValue: "1"
      searchNamespace: monitoring
      defaultDatasourceEnabled: false

  # Grafana 設定檔
  grafana.ini:
    # 設定儲存路徑（對應到持久性儲存）
    paths:
      data: /var/lib/grafana/
      logs: /var/log/grafana
      plugins: /var/lib/grafana/plugins
      provisioning: /etc/grafana/provisioning
    
    # 資料庫設定（使用內建 SQLite，儲存到持久性儲存）
    database:
      type: sqlite3
      path: /var/lib/grafana/grafana.db
    
    # 會話設定
    session:
      provider: file
      provider_config: /var/lib/grafana/sessions
    
    # 數據源設定
    datasources:
      # 禁用數據源的自動健康檢查超時
      default_http_timeout: 30s
      # 設定重試次數
      default_http_retry_attempts: 3
    
    # 日誌設定
    log:
      # 設定日誌級別，方便調試數據源問題
      level: info
      filters: ldap:debug