apiVersion: v1
kind: ConfigMap
metadata:
  name: otel-collector-mimir-config
  labels:
    app: otel-mimir-test-python-continuous
data:
  otel-collector-config.yaml: |
    receivers:
      otlp:
        protocols:
          grpc:
            endpoint: 0.0.0.0:4317
          http:
            endpoint: 0.0.0.0:4318
    exporters:
      prometheusremotewrite:
        endpoint: "http://mimir-nginx.monitoring.svc.cluster.local:80/api/v1/push"
        retry_on_failure:
          enabled: true
          initial_interval: 5s
          max_interval: 30s
          max_elapsed_time: 5m
      logging:
        loglevel: info
    processors:
      batch:
        send_batch_size: 1000
        timeout: 10s
    service:
      pipelines:
        metrics:
          receivers: [otlp]
          processors: [batch]
          exporters: [prometheusremotewrite, logging]
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: python-metric-sender-script-continuous 
  labels:
    app: otel-mimir-test-python-continuous
data:
  send_metric_continuous.py: | 
    import os
    import time
    import traceback 
    from opentelemetry import metrics
    from opentelemetry.sdk.metrics import MeterProvider
    from opentelemetry.sdk.metrics.export import PeriodicExportingMetricReader
    from opentelemetry.exporter.otlp.proto.http.metric_exporter import OTLPMetricExporter
    from opentelemetry.sdk.resources import Resource, SERVICE_NAME

    print("Python continuous metric sender starting...")
    otlp_collector_endpoint = os.getenv("OTEL_EXPORTER_OTLP_METRICS_ENDPOINT", "http://localhost:4318/v1/metrics")
    pod_name = os.getenv("HOSTNAME", "unknown_pod") 
    pod_namespace = os.getenv("POD_NAMESPACE", "default") 
    service_name = "python-continuous-sender"
    print(f"Target OTLP Collector endpoint: {otlp_collector_endpoint}")
    print(f"Pod Name: {pod_name}, Namespace: {pod_namespace}, Service Name: {service_name}")
    
    meter_provider = None 
    try:
        resource = Resource(attributes={
            SERVICE_NAME: service_name,
            "k8s.pod.name": pod_name,
            "k8s.namespace.name": pod_namespace,
            "application": "otel-mimir-continuous-test"
        })
        
        reader = PeriodicExportingMetricReader(
            OTLPMetricExporter(endpoint=otlp_collector_endpoint, timeout=10),
            export_interval_millis=1000 
        )
        meter_provider = MeterProvider(resource=resource, metric_readers=[reader])
        metrics.set_meter_provider(meter_provider)

        meter = metrics.get_meter("my.continuous.python.meter")

        requests_counter = meter.create_counter(
            name="python_continuous_app_requests_total",
            description="Total number of continuously sent test requests from the Python app.",
            unit="1"
        )

        loop_count = 0
        print("Starting continuous metric sending loop...")
        while True:
            loop_count += 1
            # Removed loop_count from attributes
            requests_counter.add(1, {"environment": "continuous_test", "status_code": "200"})
            print(f"Loop {loop_count}: Metric 'python_continuous_app_requests_total' value incremented.")
            
            time.sleep(5) 

    except KeyboardInterrupt:
        print("Python sender script interrupted by user (KeyboardInterrupt).")
    except Exception as e:
        print(f"An error occurred in Python sender: {e}")
        print("Full traceback:")
        traceback.print_exc()
        if meter_provider:
            print("Attempting to shutdown MeterProvider due to error before exiting...")
            meter_provider.shutdown()
        exit(1) 
    finally:
        if meter_provider:
            print("Shutting down MeterProvider in finally block (e.g., on normal exit if loop broken, or interrupt).")
            meter_provider.shutdown()
        print("Python continuous metric sender script is terminating.")

---
apiVersion: v1
kind: Pod
metadata:
  name: otel-mimir-test-python-continuous-pod 
  labels:
    app: otel-mimir-test-python-continuous
spec:
  restartPolicy: Never 
  volumes:
    - name: otel-collector-config-vol
      configMap:
        name: otel-collector-mimir-config 
        items:
          - key: otel-collector-config.yaml
            path: config.yaml
    - name: python-script-vol-continuous
      configMap:
        name: python-metric-sender-script-continuous 
        items:
          - key: send_metric_continuous.py 
            path: send_metric_continuous.py 
            mode: 0755

  containers:
    - name: otel-collector
      image: otel/opentelemetry-collector-contrib:0.100.0
      command: ["/otelcol-contrib"]
      args: ["--config=/etc/otelcol/config.yaml"]
      ports:
        - name: otlp-grpc
          containerPort: 4317
        - name: otlp-http
          containerPort: 4318
      volumeMounts:
        - name: otel-collector-config-vol
          mountPath: /etc/otelcol
          readOnly: true
      resources:
        limits:
          memory: "256Mi"
          cpu: "300m"
        requests:
          memory: "128Mi"
          cpu: "150m"

    - name: python-metric-sender
      image: python:3.9-slim-bookworm
      env:
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
      command: ["/bin/sh", "-c"]
      args:
        - |
          set -e
          echo "Python continuous sender container started. Installing dependencies..."
          pip install --no-cache-dir --disable-pip-version-check --quiet \
            opentelemetry-api==1.25.0 \
            opentelemetry-sdk==1.25.0 \
            opentelemetry-exporter-otlp-proto-http==1.25.0
          echo "Dependencies installed. Executing Python continuous script..."
          python /app/send_metric_continuous.py 
          echo "Python continuous script execution finished (this indicates an issue if loop was meant to be infinite)."
      volumeMounts:
        - name: python-script-vol-continuous
          mountPath: /app 
          readOnly: true
      resources:
        limits:
          memory: "128Mi"
          cpu: "250m"
        requests:
          memory: "64Mi"
          cpu: "100m"
