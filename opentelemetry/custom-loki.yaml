# Loki Stack Configuration
# 使用較新的 Loki 版本並修正配置

# 設定使用較新版本的 Loki
image:
  repository: grafana/loki
  tag: 3.4.2

loki:
  # 使用結構化配置方式
  structuredConfig:
    # 認證和授權設定
    auth_enabled: false
    
    # 服務器配置
    server:
      http_listen_port: 3100
      grpc_listen_port: 9095
      
    # 分發器配置
    distributor:
      ring:
        kvstore:
          store: inmemory
          
    # 查詢器配置
    querier:
      max_concurrent: 20
      
    # 查詢前端配置
    query_scheduler:
      max_outstanding_requests_per_tenant: 32768
      
    # 攝取器配置
    ingester:
      lifecycler:
        address: 127.0.0.1
        ring:
          kvstore:
            store: inmemory
          replication_factor: 1
        final_sleep: 0s
      chunk_idle_period: 5m
      chunk_retain_period: 30s
      max_transfer_retries: 0
      wal:
        dir: /data/loki/wal
        
    # 存儲配置
    storage_config:
      boltdb_shipper:
        active_index_directory: /data/loki/boltdb-shipper-active
        cache_location: /data/loki/boltdb-shipper-cache
        shared_store: filesystem
      filesystem:
        directory: /data/loki/chunks
        
    # 索引配置
    schema_config:
      configs:
        - from: 2020-10-24
          store: boltdb-shipper
          object_store: filesystem
          schema: v11
          index:
            prefix: index_
            period: 24h
            
    # 限制配置（重要：用於日誌保留）
    limits_config:
      retention_period: 168h  # 7天保留期限
      enforce_metric_name: false
      reject_old_samples: true
      reject_old_samples_max_age: 168h
      ingestion_rate_mb: 10
      ingestion_burst_size_mb: 20
      
    # 壓縮器配置（重要：啟用日誌保留功能）
    compactor:
      working_directory: /data/loki/retention
      shared_store: filesystem
      compaction_interval: 10m
      retention_enabled: true
      retention_delete_delay: 2h
      retention_delete_worker_count: 150
      
    # 查詢範圍配置
    query_range:
      cache_results: true
      max_retries: 5
      results_cache:
        cache:
          embedded_cache:
            enabled: true
            max_size_mb: 100

  # 持久化存儲設定
  persistence:
    enabled: true
    storageClassName: standard
    accessModes:
      - ReadWriteOnce
    size: 10Gi
    
  # 資源限制
  resources:
    limits:
      cpu: 500m
      memory: 1Gi
    requests:
      cpu: 100m
      memory: 256Mi
      
  # 健康檢查
  readinessProbe:
    httpGet:
      path: /ready
      port: http-metrics
    initialDelaySeconds: 45
    periodSeconds: 10
    timeoutSeconds: 1
    
  livenessProbe:
    httpGet:
      path: /ready
      port: http-metrics
    initialDelaySeconds: 45
    periodSeconds: 10
    timeoutSeconds: 1

# Promtail 配置 - 修正版本，使用簡化的配置避免模板錯誤
promtail:
  enabled: true
  image:
    repository: grafana/promtail
    tag: 2.9.3
    
  # 簡化配置避免模板錯誤
  config:
    # 基本配置
    logLevel: info
    serverPort: 3101
    
    # 客戶端配置 - 使用固定服務名稱避免模板錯誤
    clients:
      - url: http://loki:3100/loki/api/v1/push
        
    # 位置文件
    positions:
      filename: /run/promtail/positions.yaml
      
    # 代碼片段配置 - 使用 snippets 方式
    snippets:
      scrapeConfigs: |
        # Kubernetes Pod logs
        - job_name: kubernetes-pods
          kubernetes_sd_configs:
            - role: pod
          relabel_configs:
            - source_labels:
                - __meta_kubernetes_pod_controller_name
              regex: ([0-9a-z-.]+?)(-[0-9a-f]{8,10})?
              action: replace
              target_label: __tmp_controller_name
            - source_labels:
                - __meta_kubernetes_pod_label_app_kubernetes_io_name
                - __meta_kubernetes_pod_label_app
                - __tmp_controller_name
                - __meta_kubernetes_pod_name
              regex: ^;*([^;]+)(;.*)?$
              action: replace
              target_label: app
            - source_labels:
                - __meta_kubernetes_pod_label_app_kubernetes_io_component
                - __meta_kubernetes_pod_label_component
              regex: ^;*([^;]+)(;.*)?$
              action: replace
              target_label: component
            - action: replace
              source_labels:
              - __meta_kubernetes_pod_node_name
              target_label: node_name
            - action: replace
              source_labels:
              - __meta_kubernetes_namespace
              target_label: namespace
            - action: replace
              replacement: $1
              separator: /
              source_labels:
              - namespace
              - app
              target_label: job
            - action: replace
              source_labels:
              - __meta_kubernetes_pod_name
              target_label: pod
            - action: replace
              source_labels:
              - __meta_kubernetes_pod_container_name
              target_label: container
            - action: replace
              replacement: /var/log/pods/*$1/*.log
              separator: /
              source_labels:
              - __meta_kubernetes_pod_uid
              - __meta_kubernetes_pod_container_name
              target_label: __path__
              
  # 資源限制  
  resources:
    limits:
      cpu: 200m
      memory: 256Mi
    requests:
      cpu: 100m
      memory: 128Mi

# Fluent Bit (禁用)
fluent-bit:
  enabled: false
  
# Grafana 數據源配置
grafana:
  sidecar:
    datasources:
      enabled: false
      label: grafana_datasource