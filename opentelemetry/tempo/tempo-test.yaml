apiVersion: apps/v1
kind: Deployment
metadata:
  name: otel-cli
  namespace: monitoring
spec:
  replicas: 1
  selector:
    matchLabels:
      app: otel-cli
  template:
    metadata:
      labels:
        app: otel-cli
    spec:
      containers:
        - name: otel-cli
          image: alpine:latest
          command: ["/bin/sh", "-c"]
          args:
            - |
              # 下載並安裝 otel-cli 工具
              wget https://github.com/equinix-labs/otel-cli/releases/download/v0.4.5/otel-cli_0.4.5_linux_amd64.tar.gz -O /tmp/otel-cli.tar.gz && \
              tar -xvzf /tmp/otel-cli.tar.gz -C /usr/local/bin/ && \
              chmod +x /usr/local/bin/otel-cli && \
              apk add curl jq && \
              
              # 創建測試腳本
              cat > /usr/local/bin/send-traces.sh << 'EOF'
              #!/bin/sh
              
              # 生成隨機 trace ID
              generate_trace_id() {
                hexdump -n 16 -e '4/4 "%08x" 1 "\n"' /dev/urandom
              }
              
              # 生成隨機 span ID
              generate_span_id() {
                hexdump -n 8 -e '2/4 "%08x" 1 "\n"' /dev/urandom
              }
              
              # 使用 RFC3339 格式的時間戳
              get_current_time_rfc3339() {
                date -Iseconds
              }
              
              # 獲取未來的 RFC3339 格式時間戳
              get_future_time_rfc3339() {
                seconds=$1
                current=$(date +%s)
                future=$((current + seconds))
                date -Iseconds -d @$future 2>/dev/null || date -Iseconds
              }
              
              # 發送一個完整的 trace (包含多個 spans)
              send_complete_trace() {
                trace_id=$(generate_trace_id)
                parent_span_id=$(generate_span_id)
                
                echo "Sending trace with ID: $trace_id"
                
                # 使用 RFC3339 格式的時間戳
                current_time=$(get_current_time_rfc3339)
                
                # 父 span 使用「現在」作為開始時間，不指定結束時間（讓系統自動設定為現在）
                otel-cli span \
                  --endpoint="$OTEL_EXPORTER_OTLP_ENDPOINT" \
                  --protocol="$OTEL_EXPORTER_OTLP_PROTOCOL" \
                  --service="test-service" \
                  --name="parent-operation" \
                  --force-trace-id="$trace_id" \
                  --force-span-id="$parent_span_id" \
                  --start="$current_time" \
                  --attrs="test.attribute=value,environment=testing" \
                  --status-code="ok"
                
                sleep 0.5
                
                # 子 span 1
                child_span_id_1=$(generate_span_id)
                otel-cli span \
                  --endpoint="$OTEL_EXPORTER_OTLP_ENDPOINT" \
                  --protocol="$OTEL_EXPORTER_OTLP_PROTOCOL" \
                  --service="test-service" \
                  --name="child-operation-1" \
                  --force-trace-id="$trace_id" \
                  --force-span-id="$child_span_id_1" \
                  --force-parent-span-id="$parent_span_id" \
                  --start="$current_time" \
                  --attrs="component=database,db.operation=query" \
                  --status-code="ok"
                
                sleep 0.5
                
                # 子 span 2
                child_span_id_2=$(generate_span_id)
                otel-cli span \
                  --endpoint="$OTEL_EXPORTER_OTLP_ENDPOINT" \
                  --protocol="$OTEL_EXPORTER_OTLP_PROTOCOL" \
                  --service="test-service" \
                  --name="child-operation-2" \
                  --force-trace-id="$trace_id" \
                  --force-span-id="$child_span_id_2" \
                  --force-parent-span-id="$parent_span_id" \
                  --start="$current_time" \
                  --attrs="component=http,http.method=GET,http.url=https://example.com" \
                  --status-code="ok"
                
                sleep 0.5
                
                # 錯誤的 span
                child_span_id_3=$(generate_span_id)
                otel-cli span \
                  --endpoint="$OTEL_EXPORTER_OTLP_ENDPOINT" \
                  --protocol="$OTEL_EXPORTER_OTLP_PROTOCOL" \
                  --service="test-service" \
                  --name="error-operation" \
                  --force-trace-id="$trace_id" \
                  --force-span-id="$child_span_id_3" \
                  --force-parent-span-id="$parent_span_id" \
                  --start="$current_time" \
                  --attrs="error=true,error.message=Something went wrong" \
                  --status-code="error" \
                  --status-description="Internal server error"
                
                echo "Trace sent successfully!"
              }
              
              # 主循環：每 30 秒發送一次 trace
              while true; do
                send_complete_trace
                echo "Waiting 30 seconds before sending next trace..."
                sleep 30
              done
              EOF
              
              chmod +x /usr/local/bin/send-traces.sh && \
              
              # 執行測試腳本
              echo "Starting trace generation..." && \
              /usr/local/bin/send-traces.sh
          env:
            - name: OTEL_EXPORTER_OTLP_ENDPOINT
              value: "http://tempo.monitoring.svc.cluster.local:4317"
            - name: OTEL_EXPORTER_OTLP_PROTOCOL
              value: "grpc"
          resources:
            limits:
              cpu: "200m"
              memory: "256Mi"
            requests:
              cpu: "100m"
              memory: "128Mi"