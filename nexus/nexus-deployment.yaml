# cat nexus3/nexus3.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    k8s-app: nexus3
  name: nexus3
  namespace: resource
spec:
  replicas: 1
  selector:
    matchLabels:
      k8s-app: nexus3
  template:
    metadata:
      labels:
        k8s-app: nexus3
      name: nexus3
      namespace: resource
    spec:
      containers:
      - name: nexus3
        image: sonatype/nexus3:3.76.1
        imagePullPolicy: IfNotPresent
        ports:
          - containerPort: 8081
            name: web
            protocol: TCP
          - containerPort: 8082
            name: docker
            protocol: TCP
        livenessProbe:
          httpGet:
            path: /
            port: 8081
          initialDelaySeconds: 270
          periodSeconds: 15
          failureThreshold: 6
        readinessProbe:
          httpGet:
            path: /
            port: 8081
          initialDelaySeconds: 270
          periodSeconds: 15
          failureThreshold: 6
        resources:
          limits:
            cpu: 1000m
            memory: 2Gi
          requests:
            cpu: 500m
            memory: 512Mi
        volumeMounts:
        - name: nexus-data
          mountPath: /nexus-data
      volumes:
        - name: nexus-data
          persistentVolumeClaim:
            claimName: nexus-data-pvc

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: nexus-data-pvc
  namespace: resource
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: "standard"
  resources:
    requests:
      storage: 10Gi

---
apiVersion: v1
kind: Service
metadata:
  name: nexus3
  namespace: resource
  labels:
    k8s-app: nexus3
spec:
  selector:
    k8s-app: nexus3
  type: NodePort
  ports:
    - name: web
      protocol: TCP
      port: 8081
      targetPort: 8081
      nodePort: 30300
    - name: docker
      protocol: TCP
      port: 8082
      targetPort: 8082
      nodePort: 30301
