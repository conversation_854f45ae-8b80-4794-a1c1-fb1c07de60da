## 課程介紹
目前的軟體開發與運維環境中，容器化技術已成為主流，Kubernetes 也成為了顯學，越來越多公司的服務是容器化後運行在 Kubernetes 上。Kubernetes 作為最受歡迎的容器編排平台，提供了強大的功能來管理和擴展容器化應用程式。此外，AI Agent 的興起也讓 Kubernetes 的應用場景更加廣泛，工程師能夠透過 Kubernetes 快速複製出與 Production 類似的環境讓 LLM 與 Agent 尋找與分析 Bug 的原因或是優化程式碼的性能。
然而，對於許多開發者和運維人員來說，學習 Kubernetes 不是一件容易的事情。在入門時就會產生許多的問號，包括環境配置、資源管理、安全性和系統佈署等問題。

K8s新手村一日逃脫術是一個專為學習以 Kubernetes 為核心的開發環境建置課程。本課程使用 Kind（Kubernetes IN Docker）工具，
讓學員能夠快速在本地環境中建立 Kubernetes 環境，進行各項操作與測試。無需雲端環境或複雜硬體，只需透過 Docker 容器即可模擬完整的 Kubernetes 環境。

透過本課程，學員將能夠：
- 建立本地 Kubernetes 環境並進行基本配置
- 佈署和管理容器化應用程式
- 自產憑證實現 HTTPS 安全通訊
- 設置監控與日誌收集系統
- 實現 GitOps 自動化部署流程
- 安全管理敏感資訊

## 活動介紹

- 建立本地 Kubernetes 環境並進行基本配置
    - 建立 Kind Cluster
    - 安裝 Kind Ingress Nginx
    - Local Storage Provider 介紹與設定
- 佈署和管理容器化應用程式
    - Pod 的基本概念
    - 佈署應用程式
    - Service 與 Ingress 設定
    - 為 Host 加上 https
- 設置監控與日誌收集系統
    - Grafana 全家桶、佈署與設定
- 實現 GitOps 自動化部署流程
    - ArgoCD 介紹、佈署與設定
    - Kustomize 整合
- 安全管理敏感資訊
    - Vault 介紹、佈署與設定
    - 如何在 Kubernetes 應用

## 講師的話
在這個容器化成為主流，K8s 成為顯學的時代，工程師想要有競爭力已經不能夠只專注在寫 Code，也必須具備基礎的平台建置知識與技能。另外，現在也已經不是靠自己單打獨鬥的時代。如何讓 LLM 與 AI Agent 協助完成工作上的任務也是很重要的，但是要讓 LLM 與 AI Agent 能夠協助，最快方法就是自行搭建出與正式環境類似的開發環境，這個時候平台建置的技能就顯得非常重要。希望，能夠透過這一門課程讓參與的學員對於 K8s 有初步的認識，也知道要使用哪些軟體來管理、佈署整個平台，幫助學員夠跨過 K8s 的入門門檻。




