## 什麼是 Kind
Kind 是一個使用 Docker 容器來執行 Kubernetes 叢集的工具。它非常適合用於本地開發和測試 Kubernetes 應用程式，因為它可以在幾分鐘內建立一個 Kubernetes 叢集。

## 為什麼使用 Kind

| 特性/工具 | Docker Desktop | Kind (Kubernetes in Docker) | OrbStack |
|---------|---------------|----------------------------|----------|
| **易用性** | 高 (GUI界面友好) | 中 (命令行為主) | 高 (直觀的GUI) |
| **資源消耗** | 高 | 低 | 非常低 |
| **跨平台支援** | Windows, macOS | Windows, macOS, Linux | 僅macOS |
| **性能** | 中等 | 良好 | 優秀 |
| **配置靈活性** | 中等 | 高 | 中等 |
| **叢集的實現** | 單節點，以Docker為基礎 | 多節點，可配置高可用性叢集 | 單節點，優化的容器虛擬化 |
| **Kubernetes版本選擇** | 有限，通常只支援最新的穩定版本 | 靈活，可選擇多種版本包括最新版 | 有限，但定期更新支援較新版本 |

## Kubernetes Cluster 架構

```mermaid
graph TD
    %% Cluster 架構
    subgraph CL[" Kubernetes Cluster "]
        %% Control Panel
        subgraph CP[" Control Panel "]
            ING["Ingress Controller"]
        end
        
        %% Worker Nodes
        subgraph WN1[" Worker Node 1 "]
          subgraph NS [" Namespace "]
            subgraph DPL1["Deployment"]
                subgraph RS1["ReplicaSet"]
                    P1["Pod"]
                end
            end
            SVC1["Service 部分"]
            INGS["Ingress 部分"]
          end
        end
    end
```

### 組件說明

1. **Kubernetes Cluster**：
   - Kubernetes 的最高層級組織單位，包含 Control Panel 和多個 Worker Node
   - 提供統一管理和協調的環境，讓容器化應用能夠跨多個主機運行

2. **Control Panel**：
   - 包含 Ingress Controller，管理外部訪問集群內服務的入口點

3. **Worker Node**：
   - 實際運行應用的物理機或虛擬機
   - 每個節點都可以運行多個 Pod
   - 每個節點包含 Service 部分負責處理網絡流量

3. **Namespace**
   - 在同一個 Kubernetes Cluster 中提供虛擬的隔離環境

4. **Deployment**：
   - 聲明式 API，用於管理 Pod 的複製集(ReplicaSet)
   - 提供滾動更新、回滾等功能，使應用部署更加可靠
   - 定義所需的 Pod 狀態，Kubernetes 負責維持這個狀態

5. **ReplicaSet**：
   - 確保指定數量的 Pod 副本在任何時間運行
   - 當 Pod 失敗、被刪除或終止時，自動創建新的替代 Pod
   - 通常由 Deployment 控制器管理，不建議直接使用

6. **Pod**：
   - Kubernetes 中最小的可部署單位
   - 可以包含一個或多個容器，共享存儲和網絡資源

7. **流量和網絡組件**：
   - **Ingress Controller**：管理集群外部訪問集群內服務的入口點，處理 HTTP/HTTPS 路由
   - **Ingress**：定義 HTTP/HTTPS 路由規則
   - **Service**：為一組 Pod 提供統一的網絡訪問策略和負載均衡，每個節點都有 Service 部分來處理網絡流量

8. **節點與集群的關係**：
   - 節點是集群的工作單位，提供計算資源
   - 節點可以動態加入或離開集群
   - 集群自動處理節點故障，將工作負載重新調度到健康節點

## 軟體介紹
* kubectl
* kubecolor
* kubectx
  * 切換 Context
  ```bash
  # 列出所有可用的 Contexts
  kubectl config get-contexts

  # 查看目前的 Context
  kubectl config current-context

  # 切換 Context
  kubectl config use-context <你的-context-名稱>

  # 使用 kubectx
  kubectx
  ```
  * 切換 Namespace
  ```bash
  # 列出所有 Namespaces
  kubectl get namespaces

  # 為目前的 Context 設定預設 Namespace
  kubectl config set-context --current --namespace <你的-namespace-名稱>

  # 在單個命令中指定 Namespace
  kubectl get pod --namespace <你的-namespace-名稱>

  # 使用 kubens
  kubens <你的-namespace-名稱>
  ```
* lens

## 建立 Kind Cluster

只需要一行 Command

```
kind create cluster -n jamis-lab --image kindest/node:v1.32.2 --config ./kind.yaml
```

*   `-n jamis-lab`: 指定叢集的名稱為 `jamis-lab`，你可以變成任意名稱。
*   `--image kindest/node:v1.32.2`: 指定使用的 Kubernetes 節點映像檔版本。
*   `--config ./kind.yaml`: 指定 Kind 叢集的設定檔。

`kind.yaml` 範例：

```yaml
kind: Cluster
apiVersion: kind.x-k8s.io/v1alpha4
nodes:
  - role: worker
    extraMounts:
      - hostPath: {your path}
        containerPath: /mnt/kind 
  - role: control-plane
    extraMounts:
      - hostPath: {your path}
        containerPath: /mnt/kind 
  kubeadmConfigPatches:
  - |
    kind: InitConfiguration
    nodeRegistration:
      kubeletExtraArgs:
        node-labels: "ingress-ready=true"
  extraPortMappings:
  - containerPort: 80
    hostPort: 8080
    protocol: TCP
  - containerPort: 443
    hostPort: 8443
    protocol: TCP
```
> **注意**: 請將 `{your path}` 替換為您希望掛載的實際本機路徑。

**`kind.yaml` 設定說明補充：**

*   `extraMounts`: 此設定將指定的主機路徑 (`hostPath`) 掛載到 Kind 節點容器內的指定路徑 (`containerPath`)。這對於持久化儲存、讓 Kind 節點訪問本機文件（例如設定檔或程式碼）非常有用。範例中將 `{your path}` 掛載到所有節點的 `/mnt/kind`。
*   `kubeadmConfigPatches`: 允許您自訂 Kubeadm 的配置。範例中，透過 `kubeletExtraArgs` 為節點添加了 `ingress-ready=true` 標籤。這個標籤常被 Nginx Ingress Controller 等元件用作 `nodeSelector`，以確保 Ingress Controller Pod 被調度到特定的節點上。

在 Kind 中，`extraPortMappings` 設定是訪問集群服務的重要配置：

1. **端口映射的必要性**
   - Kind 使用 Docker 容器來模擬 Kubernetes 節點
   - 沒有 `extraPortMappings` 設定，主機將無法直接訪問集群內的服務
   - 每個需要從主機訪問的服務都需要明確配置端口映射

2. **設定格式**
```yaml
extraPortMappings:
  - containerPort: 80    # 容器內部端口 (例如 Ingress Controller 的 HTTP 端口)
    hostPort: 8080      # 映射到主機的端口 (範例值，可依需求修改)
    listenAddress: "127.0.0.1"  # 監聽地址：127.0.0.1 僅本機訪問，0.0.0.0 允許外部訪問
    protocol: TCP       # 協議：TCP 或 UDP
  - containerPort: 443   # 容器內部端口 (例如 Ingress Controller 的 HTTPS 端口)
    hostPort: 8443      # 映射到主機的端口 (範例值，可依需求修改)
    protocol: TCP
```
> **說明**: 範例中的 `hostPort: 8080` 和 `hostPort: 8443` 是主機上用於訪問容器內 80 和 443 端口的端口號，您可以根據需要或避免端口衝突進行修改。

## Kind K8s Cluster Network 說明

以下的圖表示在 Kind K8s Cluster 的架構下，網路的流量是如何傳送

```mermaid
%%{init: {'theme': 'dark', 'themeVariables': { 'fontSize': '20px', 'primaryColor': '#d3d3d3', 'primaryTextColor': '#fff', 'primaryBorderColor': '#fff', 'lineColor': '#fff', 'secondaryColor': '#d3d3d3', 'tertiaryColor': '#d3d3d3' }}}%%
graph TD
    %% 主機系統層
    HostPort["主機系統端口<br>(80, 443)"]:::greyStyle
    
    %% Docker 容器端口層
    ContainerPort["Docker 容器端口"]:::greyStyle
    
    %% Kubernetes 網路層
    subgraph KubeNet["Kubernetes網路機制"]
        subgraph ServiceNet["Service 網路層"]
            Ingress["Ingress Controller"]:::greyStyle
            NodePort["NodePort Service"]:::greyStyle
            ClusterIP["ClusterIP Service"]:::greyStyle
            
            Ingress --> ClusterIP
        end
        
        subgraph PodNet["Pod 網路層"]
            Pod1["Pod 1"]:::greyStyle
            Pod2["Pod 2"]:::greyStyle
            Pod3["Pod 3"]:::greyStyle
        end
        
        %% 服務連接到 Pod
        ClusterIP --> Pod1
        ClusterIP --> Pod2
        NodePort --> Pod2
        NodePort --> Pod3
    end
    
    %% 網路流量路徑
    HostPort -->|"Port Mapping"| ContainerPort
    ContainerPort -->|"轉送"| Ingress
    ContainerPort -->|"轉送"| NodePort

classDef default fontSize:20px;
classDef greyStyle fill:#d3d3d3,stroke:#333,stroke-width:2px,color:#333;
```

## 網絡層級關係

- **網路流量路徑**：
  - 外部請求 → 主機系統端口 → Docker 容器端口 → Kubernetes 服務
  - Port Mapping 實現主機與容器間的通信橋接
  - 容器端口可轉送流量至 Ingress 或 NodePort 服務

- **Pod 網路層**：實現 Pod 之間的直接通信
  - 由 CNI 插件管理（如 Calico、Flannel）
  - 每個 Pod 擁有唯一 IP，可直接訪問
  - 實現扁平化網路，跨節點通信

- **Service 網路層**：提供服務發現和負載均衡
  - 提供穩定服務端點，隱藏 Pod 變化
  - 服務類型：
    - **ClusterIP**：僅集群內可訪問
    - **NodePort**：在節點開放固定端口，直接路由到 Pod

## 安裝 Kind Ingress Nginx

Kind Ingress Nginx 是一個用於 Kind 叢集的 Ingress 控制器，它允許您從叢集外部訪問您的服務。

``` shell
kubectl apply -f kind-ingress-nginx.yaml
```

如果有多個節點，請在剛剛的 YMAL 內找到 `kind: deployment` 的部分加上 `nodeSelector`，將 Ingress Nginx 部署到控制平面節點。

```
spec:
  minReadySeconds: 0
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app.kubernetes.io/component: controller
      app.kubernetes.io/instance: ingress-nginx
      app.kubernetes.io/name: ingress-nginx
  strategy:
    rollingUpdate:
      maxUnavailable: 1
    type: RollingUpdate
  template:
    metadata:
      labels:
        app.kubernetes.io/component: controller
        app.kubernetes.io/instance: ingress-nginx
        app.kubernetes.io/name: ingress-nginx
        app.kubernetes.io/part-of: ingress-nginx
        app.kubernetes.io/version: 1.12.0-beta.0
    spec:
      nodeSelector:
          kubernetes.io/hostname: {your cluster name}-control-plane
```

請將 `{your cluster name}` 替換為您的叢集名稱。

> 佈署完成後請使用 `kubectl get pod -o wide` 確認 `ingress-nginx-controller` 有佈署在正確的 node 上

## 變更 local-path-storage configmap

建立 kind cluster 後，預設也會佈署 local-path-storage 的 pod，local-path-storage 是一個 Kubernetes 儲存體供應商，它允許您使用節點上的本機儲存體。

```
# 切換 namespace 
kubens local-path-storage

# 取得 configMaps 清單
kubectl get configMaps

# 修改 configMaps 內容
kubectl edit configMap local-path-config
```

將 `nodePathMap` 裡的 paths 修改為 `/mnt/kind`。

修改後的 `local-path-config` 範例：

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: local-path-config
  namespace: kube-system
data:
  nodePathMap: |
    {
      "node":"DEFAULT_PATH_FOR_NON_LISTED_NODES",
      "paths":["/mnt/kind"]
    }
```

修改完 configmap 後，需要刪除 local-path-provisioner 的 Pod，讓它重新建立並使用新的設定。
```bash
kubectl get pod
kubectl delete pod {pon name}
```

### 驗證 local-path-storage
我們可以透過建立一個 pvc 和 pod 來驗證剛剛修改的 configMap 是否正確

```
# ./local-path-provisioner
kubectl apply -f test-pvc.yaml
kubectl get pvc

kubectl apply -f test-pod.yaml
kubectl get pod
```

等 pod 成功運行後

```
kubectl exec test-local-pod -- cat /data/test.txt
```

看到 `Hello from local storage` 代表 local-path-provisioner 正常運行

## 建立 lab namespace & 佈署第一個 Pod

### Create Namespace
Namespace 是一個 Kubernetes 的虛擬集群，用於隔離資源。

```
kubectl create namespace lab
kubens lab
```

建立 `lab` namespace，用於佈署後續會使用到的資源。

### Deployment your applicaiton

Pod 是 Kubernetes 中最小的可部署單元，它是建立在 Kubernetes 叢集上的最基本構建區塊。理解 Pod 的概念對於管理 Kubernetes 應用程式至關重要。

### Pod 的基本概念

* **定義**：Pod 是 Kubernetes 中最小的可部署單元
* **共享資源**：Pod 是一組容器的集合，這些容器共享相同的網路和存儲資源
* **多容器設計**：Pod 可以包含一個或多個容器，這些容器可以是相同或不同的映像

以下是一個基本的 Deployment 配置，用於部署一個簡單的應用程式到 `lab` 命名空間：

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: demo-api
  namespace: lab
spec:
  replicas: 1
  selector:
    matchLabels:
      app: demo-api
  template:
    metadata:
      labels:
        app: demo-api
    spec:
      containers:
      - name: demo-container
        image: ghcr.io/jamisliao/lab/demo:*******
        ports:
        - containerPort: 8080
        resources:
          requests:
            memory: "64Mi"
            cpu: "100m"
          limits:
            memory: "128Mi"
            cpu: "200m"
```

#TODO 
* 說明 `selector`, `template`
* labels 和 annotations 有什麼差別

佈署服務
```bash
# ./deploy/http/
kubectl apply -f deployment.yaml
```

### 佈署完畢後測試

透過 lens 設定完 port-forward 後，可以透過以下指令存取 API

```bash
# MacOS
curl http://localhost:{port-forward}/weatherforecast

# Windows Terminal
Invoke-WebRequest -Uri "http://localhost:{port-forward}/weatherforecast" -Method Get
```

## Service 與 Ingress 設定

### Service 服務類型解析

Kubernetes Service 是連接 Pod 和外部網路的橋樑。Service 為一組 Pod 提供穩定的網路端點，無論這些 Pod 如何變化，Service 的 IP 和 DNS 名稱都保持不變，實現了服務發現和負載平衡。

#### ClusterIP Service

ClusterIP 是默認的 Service 類型，提供一個只能在叢集內部訪問的穩定 IP 地址。

``` mermaid
graph TD
    subgraph "Kubernetes 叢集"
        subgraph "ClusterIP Service"
            A[Pod 1] --- C{ClusterIP<br>Service}
            B[Pod 2] --- C
            C -.- D[內部IP: 10.96.x.x]
        end
        
        E[其他 Pod] -.-> |存取| C
        F[kube-dns] -.-> |"service.namespace.svc.cluster.local"| C
    end
    
    style C fill:#ffffff,stroke:#000000,stroke-width:2px,color:#000000
    style D fill:#ffffff,stroke:#000000,stroke-width:2px,color:#000000
    style A fill:#ffffff,stroke:#000000,stroke-width:2px,color:#000000
    style B fill:#ffffff,stroke:#000000,stroke-width:2px,color:#000000
    style E fill:#ffffff,stroke:#000000,stroke-width:2px,color:#000000
    style F fill:#ffffff,stroke:#000000,stroke-width:2px,color:#000000
```

**ClusterIP 服務特性：**

1. **僅叢集內部可訪問**：ClusterIP 提供的 IP 地址僅在 Kubernetes 叢集內部可訪問。
2. **穩定的服務發現**：自動註冊到 Kubernetes DNS，可使用 `服務名稱.命名空間.svc.cluster.local` 訪問。
3. **自動負載平衡**：流量會被平均分配到所有符合選擇器標籤的 Pod。
4. **適用場景**：適合叢集內部微服務之間的通訊。

**ClusterIP 設定範例：**

```yaml
apiVersion: v1
kind: Service
metadata:
  name: demo-api-service
  namespace: lab
spec:
  type: ClusterIP  # 可省略，此為預設類型
  selector:
    app: demo-api  # 選擇標籤為 app=demo-api 的所有 Pod
  ports:
    - port: 80     # 服務暴露的端口
      targetPort: 8080  # Pod 上的目標端口
      protocol: TCP
```

佈署 Service
```bash
# ./deploy/http/
kubectl apply -f ./service-clusterip.yaml
```

**存取方式：**

佈署 test pod
```bash
kubectl apply -f ./test-pod.yaml
```

測試 service 是否正常運作
```bash
# 從叢集內的 Pod 測試
kubectl get pod
kubectl exec -it -n lab [pod-name] -- curl  http://demo-api-service.lab/weatherforecast

# 使用 lens 將  開發測試 (將服務端口轉發到本機)
# MacOS
curl http://localhost:{port-forward}/weatherforecast

# Windows Terminal
Invoke-WebRequest -Uri "http://localhost:{port-forward}/weatherforecast" -Method Get
```

#### NodePort Service

NodePort 服務建立在 ClusterIP 之上，它在叢集中每個節點上開放一個固定端口，允許從外部直接訪問服務。

``` mermaid
graph TD
    subgraph "Kubernetes 叢集"
        subgraph "NodePort Service"
            A[Pod 1] --- C{NodePort<br>Service}
            B[Pod 2] --- C
            C -.- D[內部同樣有 ClusterIP]
        end
        
        subgraph "Node 1"
            N1[NodePort: 30080] -.-> C
        end
        
        subgraph "Node 2"
            N2[NodePort: 30080] -.-> C
        end
        
        E[其他 Pod] -.-> C
    end
    
    subgraph "叢集外部"
        H[外部使用者] --> M[可以透過<br>節點IP:NodePort<br>存取]
        M --> N1
        M --> N2
    end
    
    style C fill:#ffffff,stroke:#000000,stroke-width:2px,color:#000000
    style D fill:#ffffff,stroke:#000000,stroke-width:2px,color:#000000
    style A fill:#ffffff,stroke:#000000,stroke-width:2px,color:#000000
    style B fill:#ffffff,stroke:#000000,stroke-width:2px,color:#000000
    style N1 fill:#ffffff,stroke:#000000,stroke-width:2px,color:#000000
    style N2 fill:#ffffff,stroke:#000000,stroke-width:2px,color:#000000
    style M fill:#ffffff,stroke:#000000,stroke-width:2px,color:#000000
    style E fill:#ffffff,stroke:#000000,stroke-width:2px,color:#000000
    style H fill:#ffffff,stroke:#000000,stroke-width:2px,color:#000000
```

**NodePort 服務特性：**

1. **外部直接訪問**：可以通過 `<節點IP>:<NodePort>` 從外部訪問服務。
2. **保留 ClusterIP 功能**：NodePort 也創建一個 ClusterIP，保留了叢集內部訪問能力。
3. **端口限制**：NodePort 必須在 30000-32767 範圍內。
4. **一致性**：同一服務的 NodePort 在所有節點上相同。
5. **適用場景**：開發測試環境或簡單的生產部署。

**NodePort 設定範例：**

```yaml
apiVersion: v1
kind: Service
metadata:
  name: demo-api-nodeport
  namespace: lab
spec:
  type: NodePort     # 指定為 NodePort 類型
  selector:
    app: demo-api
  ports:
    - port: 80          # 叢集內部訪問的端口
      targetPort: 8080  # Pod 上的目標端口
      nodePort: 30301   # 在節點上開放的端口，必須在 30000-32767 範圍內
      protocol: TCP
```
佈署 Service
```bash
kubectl apply -f ./service-nodeport.yaml
```

**Kind 環境特別注意事項：**

Kind 使用容器模擬 Kubernetes 節點，在建立叢集時需要特別設定端口映射以支持 NodePort 功能：

```yaml
# kind_local.yaml 中的設定
extraPortMappings:
- containerPort: 30301  # 容器內的 NodePort
  hostPort: 30301       # 映射到主機的端口
  protocol: TCP
```

**存取方式：**

```bash
# MacOS
curl http://127.0.0.1:30301/weatherforecast

# Windows Terminal
Invoke-WebRequest -Uri "http://127.0.0.1:30301/weatherforecast" -Method Get
```

#### ClusterIP 與 NodePort 比較

| 特性 | ClusterIP | NodePort |
|---------|-----------|----------|
| 存取範圍 | 僅叢集內部可訪問 | 可從外部直接訪問 |
| 網路端點 | 穩定內部 IP 與 DNS 名稱 | 在每個節點上開放相同端口 |
| 安全性 | 不暴露到叢集外部，安全性較高 | 端口暴露在外部，需要額外安全措施 |
| 端口限制 | 無特定端口限制 | 端口範圍限制在 **30000-32767** 之間 ⚠️ |
| 功能關係 | 基礎服務類型 | 包含 ClusterIP 的所有功能 |
| 適用場景 | 適合內部微服務通訊 | 適合開發與測試環境 |


**何時選擇哪種服務類型？**

* **選擇 ClusterIP 當：**
  - 服務僅需要在叢集內部被訪問
  - 想要保持服務不對外暴露以提高安全性
  - 服務是內部基礎設施組件（如資料庫、快取等）

* **選擇 NodePort 當：**
  - 需要從叢集外部直接訪問服務
  - 處於開發或測試環境
  - 不需要複雜的負載平衡或路由規則
  - 沒有可用的 Ingress 控制器

#### 為你的服務綁上 Domain

Ingress 是 Kubernetes 中管理外部訪問服務的物件，通常處理 HTTP/HTTPS 路由。你可以把它想像成 Cluster 內服務路由器，可以將域名對應到指定的服務。
Ingress 設定範例

```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: demo-api-ingress
  namespace: lab
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  ingressClassName: nginx
  rules:
  - host: demo.lab.dev  # 您可以修改為您希望的主機名
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: demo-api-service  # 這裡引用了我們剛剛創建的 service
            port:
              number: 80
```

```bash
# ./deploy/http/
kubectl apply -f ingress.yaml
```

驗證是否生效

```bash
# MacOS
curl http://demo.lab.dev:8080/weatherforecast

# Windows Terminal
Invoke-WebRequest -Uri "http://demo.lab.dev:8080/weatherforecast" -Method Get
```

### 為 Domain 加上 https

本節將說明如何為 Kubernetes 中的服務加上 HTTPS，主要分為三個步驟：
1. 產生自簽憑證（Self-signed Certificate）
2. 將憑證加入到 Kubernetes Secret
3. 修改 Ingress 設定以啟用 HTTPS

#### 1. 產生自簽憑證

使用 OpenSSL 工具產生自簽憑證。推薦使用通配符證書（帶有 `*` 前綴）以支援所有子域名。在終端機中執行以下命令：

```bash
# 產生私鑰
# MacOS
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
  -keyout tls.key -out tls.crt \
  -subj "/CN=*.lab.dev" \
  -addext "subjectAltName=DNS:*.lab.dev" \
  -addext "keyUsage=digitalSignature,keyEncipherment" \
  -addext "extendedKeyUsage=serverAuth"

# Windows Terminal
openssl req -x509 -nodes -days 365 -newkey rsa:2048 `
  -keyout tls.key -out tls.crt `
  -subj "/CN=*.lab.dev" `
  -addext "subjectAltName=DNS:*.lab.dev" `
  -addext "keyUsage=digitalSignature,keyEncipherment" `
  -addext "extendedKeyUsage=serverAuth"
```

> 此設定將創建一個通配符憑證，支援所有 `*.lab.dev` 形式的子域名（如 `demo.lab.dev`、`grafana.lab.dev`、`vault.lab.dev` 等）。通配符憑證大大簡化了多子域名環境的 SSL 配置。

#### 2. 將憑證加入到 Kubernetes Secret

將產生的憑證文件加入到 Kubernetes Secret 資源中：

```bash
# 創建 TLS 類型的 Secret
# Mac OS
kubectl create secret tls lab-tls-secret \
  --key tls.key \
  --cert tls.crt \
  --namespace lab

# Windows Terminal
kubectl create secret tls lab-tls-secret `
  --key tls.key `
  --cert tls.crt `
  --namespace lab
```

或者，可以使用 YAML 文件定義 Secret：

```yaml
apiVersion: v1
kind: Secret
metadata:
  name: your-tls-secret
  namespace: your-namespace
type: kubernetes.io/tls
data:
  tls.crt: BASE64_ENCODED_CERTIFICATE
  tls.key: BASE64_ENCODED_KEY
```

要獲取 BASE64 編碼的憑證和私鑰，執行：

```bash
# Mac OS
cat tls.crt | base64 -w 0
cat tls.key | base64 -w 0

# Windows Terminal
[Convert]::ToBase64String([IO.File]::ReadAllBytes("{.crt file path}"))
[Convert]::ToBase64String([IO.File]::ReadAllBytes("{.key file path}"))
```

將輸出的內容分別填入 YAML 文件中的 `tls.crt` 和 `tls.key` 欄位，然後使用 `kubectl apply -f your-secret.yaml` 創建 Secret。

新增完畢後，可以透過以下指令確認
```bash
kubectl get secrets
```

如果有看到以下資訊即為成功新增
```bash
NAME             TYPE                DATA   AGE
lab-tls-secret   kubernetes.io/tls   2      2s
```

#### 3. 修改 Ingress 以啟用 HTTPS

最後，修改 Ingress 資源以使用剛剛創建的 TLS Secret：

```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: your-ingress
  namespace: your-namespace
  annotations:
    # 選擇 nginx ingress controller
    kubernetes.io/ingress.class: "nginx"
    # 可選：強制 HTTP 重定向到 HTTPS
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    # 可選：設定 SSL 協議
    nginx.ingress.kubernetes.io/ssl-protocols: "TLSv1.2 TLSv1.3"
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - your-domain.com
    secretName: your-tls-secret
  rules:
  - host: your-domain.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: your-service
            port:
              number: 80
```

替換：
- `your-ingress`：您的 Ingress 名稱
- `your-namespace`：您的 namespace
- `your-domain.com`：您的網域名稱（必須與憑證中的網域相符）
- `your-tls-secret`：您創建的 TLS Secret 名稱
- `your-service`：您希望以 HTTPS 方式暴露的服務名稱

應用修改後的 Ingress 配置：

```bash
# ./deploy/https/
kubectl apply -f ingress.yaml
```

#### 驗證 HTTPS 設定

設定完成後，可以使用以下方法驗證 HTTPS 是否運作正常：

```bash
# 檢查 Ingress 狀態
kubectl get ingress -n lab

# 測試 HTTPS 連接
# MacOS
curl -k https://demo.lab.dev:8443/weatherforecast

# Windows Terminal
Invoke-WebRequest -SkipCertificateCheck https://demo.lab.dev:8443/weatherforecast -Method Get
Invoke-RestMethod -Uri https://demo.lab.dev:8443/weatherforecast -SkipCertificateCheck
```

以上測試是需要修改系統的 `hosts` 檔案，檔案位置在

* MacOS: `/etc/hosts`
* Windows: `C:\Windows\System32\drivers\etc\hosts`

請使用編輯器在內容新增

```
127.0.0.1 demo.lab.dev
127.0.0.1 grafana.lab.dev
```

>注意：由於使用的是自簽憑證，瀏覽器可能會顯示不安全的連接警告。在生產環境中，建議使用由受信任的憑證授權機構（CA）簽發的憑證。


## Image Registry 選擇

K8s 只能夠從 Image Registry 上拉取 Image 來進行佈署，所以當在 K8s 上需要佈署 Pod 時，需要將 Image 上傳到 Image Registry 供佈署使用。你有以下幾個選擇：

1. **自建 Image Registry**：
   - 使用 Nexus（[查看 Nexus 安裝和設定指南](nexus-and-containerd-guide.md)）
   - 完全控制和自訂能力
   - 需要自行維護

2. **使用 GitHub Packages**：
   - GitHub 官方提供的容器註冊表服務
   - 與 GitHub 完整整合
   - 支援私有和公開映像檔

### 使用 GitHub Packages 作為 Container Registry

GitHub Packages 提供了容器註冊表服務，可以用來存放和分享 Docker 映像檔。以下是使用步驟：

#### 創建 GitHub 個人存取令牌 (Personal Access Token)

1. 登入 GitHub 帳號
2. 點擊右上角頭像 → Settings → Developer settings → Personal access tokens → Tokens (classic) → Generate new token
3. 提供描述，例如「Docker GitHub Packages」
4. 選擇令牌的有效期限
5. 勾選以下權限：
   - `repo` (所有子權限)
   - `write:packages`
   - `read:packages`
6. 點擊「Generate token」，並複製生成的令牌

#### Docker 登入至 GitHub Packages

```bash
# 使用個人存取令牌登入
docker login ghcr.io -u YOUR_GITHUB_USERNAME -p YOUR_GITHUB_TOKEN
```

#### 打包和推送 Image

```bash
# /kind-lab/demo/src
docker build -t ghcr.io/{your name}/lab/demo:0.0.0.0 -f ./demo-api/Dockerfile .

# 推送 image
docker push ghcr.io/{your name}/lab/demo:0.0.0.0
```

#### 設定 Package 可見性

1. 前往 GitHub Package 頁面
2. 選擇你的 package
3. 點擊 "Package settings"
4. 在 "Danger Zone" 找到 "Change visibility"
5. 選擇 "Public" 或保持 "Private"

## 安裝 Grafana 相關服務
Grafana Stack 提供完整的可觀測性解決方案，包含以下核心元件。在開始安裝前，讓我們先了解 Helm 這個工具。

### 何謂 Helm

Helm 是 Kubernetes 的套件管理工具，類似於 Mac 的 Homebrew、Windows powershell 的 chocolatey 或 Node.js 的 npm。它主要用於：

1. **簡化部署**：
   - 將複雜的 Kubernetes 應用打包成易於管理的 Chart
   - 支援一鍵安裝和移除
   - 提供版本控制和回滾功能

2. **標準化配置**：
   - 通過變數和模板實現配置重用
   - 支援多環境部署
   - 確保部署的一致性

### Helm 的核心概念

1. **Chart**：
    - Helm 的基本單位，包含：
      * Kubernetes 資源定義
      * 配置模板
      * 預設值和文檔
    - 類似於軟體的安裝包
    - 核心目標：打包與重用
	    * 打包 (Packaging): Chart 將構成應用程式的所有 Kubernetes 資源描述檔 (YAML)、配置以及依賴關係，全部打包在一起，形成一個獨立、可管理的單元。
      * 重用 (Reusability): 一旦製作好一個 Chart，您就可以在不同的環境 (開發、測試、生產)、不同的專案，甚至分享給社群，讓其他人也能快速部署相同的應用程式。
    - Chart 的內部結構 (一個 Chart 資料夾裡通常有什麼)：想像您解壓縮一個 Chart 的 ⁠.tgz 檔案 (或者直接看一個 Chart 的原始碼資料夾)，您通常會看到以下結構：
      * ⁠Chart.yaml (必要):
        * 這是 Chart 的「身份證」或「封面」。
        * 它是一個 YAML 檔案，包含了 Chart 的元數據 (metadata)
      * templates/ (必要):
        * 這是 Chart 的「主要內容」或「食譜的步驟」。	▪	這個資料夾存放了所有 Kubernetes 資源的範本檔案 (template files)。這些檔案通常是 YAML 格式，但它們不是靜態的 YAML。
        * 它們使用了 Go 範本語言 (Go templating language)，裡面會包含佔位符 (placeholders) 和一些邏輯。例如，⁠{{ .Values.replicaCount }} 就是一個佔位符，它的實際值會從 ⁠values.yaml 檔案中讀取。
        * 常見的範本檔案可能包括 ⁠deployment.yaml, ⁠service.yaml, ⁠configmap.yaml, ⁠ingress.yaml 等。
      * ⁠values.yaml (必要):
        * 這是 Chart 的「DIY 套件的預設配置」。
        * 這個 YAML 檔案提供了 ⁠templates/ 中所有範本所需的預設值 (default values)。
        * 當您安裝 Chart 而沒有提供任何自訂配置時，Helm 就會使用這裡的預設值來填補範本中的佔位符。
        * 可以把它看作是 Chart 作者提供的一組推薦配置。

2. **Repository**：
   - Chart 的倉庫
   - 可以添加多個倉庫源
   - 支援私有倉庫

3. **Release**：
   - Chart 的運行實例
   - 每次安裝產生唯一的 Release
   - 可以同時運行多個 Release

### Helm 基本使用

1. **Repository 管理**：
```bash
# 添加倉庫
helm repo add [名稱] [URL]

# 更新倉庫
helm repo update

# 列出已添加的倉庫
helm repo list

# 移除倉庫
helm repo remove [名稱]
```
example: https://grafana.github.io/helm-charts

2. **Chart 操作**：
```bash
# 搜尋 Chart
helm search repo [關鍵字]

# 列出 Chart 的可用版本
helm search repo [Chart名稱] --versions

# 查看 Chart 的詳細信息
helm show values [Chart名稱]
```

3. **Release 管理**：
```bash
# 安裝 Chart
helm install [Release名稱] [Chart名稱] -f values.yaml

# 更新 Release
helm upgrade [Release名稱] [Chart名稱] -f values.yaml

# 移除 Release
helm uninstall [Release名稱]

# 列出已安裝的 Release
helm list
```

### 環境準備

先建立一個給監控專用的 namespace
```bash
kubectl create namespace monitoring
kubens monitoring
```

新增必要的 Helm Repository：
新增 Helm Repository：
```bash
helm repo add grafana https://grafana.github.io/helm-charts
helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
helm repo update
```

### 元件說明與安裝步驟

#### 1. Grafana & Prometheus - 指標收集
- 功能：收集和存儲時間序列數據
- 安裝指令：
```bash
# /opentelemetry
helm install prometheus prometheus-community/kube-prometheus-stack -f custom-prometheus.yaml
```
- 配置說明：custom-prometheus.yaml 包含了自定義的監控目標和告警規則

#### 2. Loki - 日誌聚合系統
- 功能：集中管理和分析容器日誌
- 安裝指令：
```bash
helm install loki grafana/loki-stack -f custom-loki.yaml
```
- 配置文件：`custom-loki.yaml` 包含了持久化存儲和擴展設定

#### 4. Mimir - 指標長期存儲
- 功能：提供高可用性的指標數據長期存儲解決方案
- 安裝指令：
```bash
helm install mimir grafana/mimir-distributed -f custom-mimir.yaml
```
- 配置文件：`custom-mimir.yaml` 定義了存儲和擴展參數

#### 4. Tempo - 分散式追蹤
- 功能：收集和分析分散式系統的追蹤數據
- 安裝指令：
```bash
# ./opentelemetry/tempo
helm install tempo grafana/tempo -f custom-tempo.yaml
```
### 驗證安裝
1. 檢查所有 Pod 狀態：
```bash
# MacOS
kubectl get pods | grep -E 'loki|mimir|tempo|prometheus|grafana'

# Windows Terminal
kubectl get pods
```

2. 檢查服務可用性：
```bash
# MacOS
kubectl get svc | grep -E 'loki|mimir|tempo|prometheus|grafana'

# Windows Terminal
kubectl get svc
```

### 配置數據源
完成安裝後，需要在 Grafana 中配置以下數據源：

1. Mimir（指標數據）
   - URL: http://mimir-nginx/prometheus
   - 類型: Prometheus

2. Tempo（追蹤數據）
   - URL: http://tempo:3100
   - 類型: Tempo

3. Loki（日誌數據）
   - URL: http://loki:3100
   - 類型: Loki

> 瀏覽器遇到 https 的憑證問題可以使用 `certificateErrorPageController.proceed();` 來解決

### 驗證 Mimir & Tempo 是否正常運作

驗證方法：
* **Tempo**

```bash
# 部署測試應用
# /opentelemetry/tempo
kubectl apply -f tempo-test.yaml

kubectl logs -f {pod name}
```

佈署成功後應該在 `logs` 可以看到類似的資訊

```bash
Connecting to github.com (20.27.177.113:443)
Connecting to objects.githubusercontent.com (185.199.111.133:443)
saving to '/tmp/otel-cli.tar.gz'
otel-cli.tar.gz       11% |***                             |  591k  0:00:07 ETA
otel-cli.tar.gz      100% |********************************| 4989k  0:00:00 ETA
'/tmp/otel-cli.tar.gz' saved
CHANGELOG.md
LICENSE
README.md
otel-cli
fetch https://dl-cdn.alpinelinux.org/alpine/v3.21/main/aarch64/APKINDEX.tar.gz
fetch https://dl-cdn.alpinelinux.org/alpine/v3.21/community/aarch64/APKINDEX.tar.gz
(1/11) Installing brotli-libs (1.1.0-r2)
(2/11) Installing c-ares (1.34.5-r0)
(3/11) Installing libunistring (1.2-r0)
(4/11) Installing libidn2 (2.3.7-r0)
(5/11) Installing nghttp2-libs (1.64.0-r0)
(6/11) Installing libpsl (0.21.5-r3)
(7/11) Installing zstd-libs (1.5.6-r2)
(8/11) Installing libcurl (8.12.1-r1)
(9/11) Installing curl (8.12.1-r1)
(10/11) Installing oniguruma (6.9.9-r0)
(11/11) Installing jq (1.7.1-r0)
Executing busybox-1.37.0-r12.trigger
OK: 13 MiB in 26 packages
Starting trace generation...
Sending trace with ID: 22cfcadaa825c0253494c75d2ea70088
Trace sent successfully!
Waiting 30 seconds before sending next trace...
Sending trace with ID: 0e786f2243182bfc246bf3306184d538
Trace sent successfully!
```

接著就可以在 Grafana 中的 Explore 查。注意：要加 data source 切換到 **Tempo**

* **Mimir**

```bash
# 部署測試應用
# /opentelemetry
kubectl apply -f mimir-test.yaml

kubectl get pod
kubectl logs -f {pod name}
```

佈署成功後應該在 `logs` 可以看到類似的資訊

```bash
Defaulted container "otel-collector" out of: otel-collector, python-metric-sender
2025-05-18T04:28:01.472Z	info	service@v0.100.0/service.go:102	Setting up own telemetry...
2025-05-18T04:28:01.544Z	info	service@v0.100.0/telemetry.go:103	Serving metrics	{"address": ":8888", "level": "Normal"}
2025-05-18T04:28:01.544Z	info	exporter@v0.100.0/exporter.go:275	Deprecated component. Will be removed in future releases.	{"kind": "exporter", "data_type": "metrics", "name": "logging"}
2025-05-18T04:28:01.544Z	warn	common/factory.go:68	'loglevel' option is deprecated in favor of 'verbosity'. Set 'verbosity' to equivalent value to preserve behavior.	{"kind": "exporter", "data_type": "metrics", "name": "logging", "loglevel": "info", "equivalent verbosity level": "Normal"}
2025-05-18T04:28:01.545Z	info	service@v0.100.0/service.go:169	Starting otelcol-contrib...	{"Version": "0.100.0", "NumCPU": 12}
2025-05-18T04:28:01.545Z	info	extensions/extensions.go:34	Starting extensions...
2025-05-18T04:28:01.545Z	warn	internal@v0.100.0/warning.go:42	Using the 0.0.0.0 address exposes this server to every network interface, which may facilitate Denial of Service attacks. Enable the feature gate to change the default and remove this warning.	"kind": "receiver", "name": "otlp", "data_type": "metrics", "documentation": "https://github.com/open-telemetry/opentelemetry-collector/blob/main/docs/security-best-practices.md#safeguards-against-denial-of-service-attacks", "feature gate ID": "component.UseLocalHostAsDefaultHost"}
2025-05-18T04:28:01.545Z	info	otlpreceiver@v0.100.0/otlp.go:102	Starting GRPC server	{"kind": "receiver", "name": "otlp", "data_type": "metrics", "endpoint": "0.0.0.0:4317"}
2025-05-18T04:28:01.546Z	warn	internal@v0.100.0/warning.go:42	Using the 0.0.0.0 address exposes this server to every network interface, which may facilitate Denial of Service attacks. Enable the feature gate to change the default and remove this warning.	"kind": "receiver", "name": "otlp", "data_type": "metrics", "documentation": "https://github.com/open-telemetry/opentelemetry-collector/blob/main/docs/security-best-practices.md#safeguards-against-denial-of-service-attacks", "feature gate ID": "component.UseLocalHostAsDefaultHost"}
2025-05-18T04:28:01.546Z	info	otlpreceiver@v0.100.0/otlp.go:152	Starting HTTP server	{"kind": "receiver", "name": "otlp", "data_type": "metrics", "endpoint": "0.0.0.0:4318"}
2025-05-18T04:28:01.546Z	info	service@v0.100.0/service.go:195	Everything is ready. Begin running and processing data.
```

接著就可以在 Grafana 中的 Explore 查。注意：要加 data source 切換到 **mimir**，metrics 的名稱為 **python_continuous_app_requests_total**

#### 5. Grafana Enterprise Metrics (Alloy)
- 功能：企業級的指標管理平台
- 安裝指令：
```bash
# /opentelemetry/alloy
helm install alloy grafana/alloy -f custom-alloy.yaml
```

確認 helm 安裝完成後，請執行以下的 bash
```bash
# /opentelemetry/alloy
kubectl apply -f alloy-ext-svc.yaml
kubectl apply -f alloy-ingress.yaml
```

以上這兩個 yaml 檔的設定可以讓瀏覽器開啟 alloy 的管理介面，網址為 `https://alloy.lab.dev:8443`。會開啟 `30317` & `30327` 這兩個端口來接收 OTEL 的資料。

#### 驗證 Alloy 是否正常運作

要驗證 Alloy 是否正常運作需要佈署一個 Pod 來發送測試資料

```bash
# /opentelemetry/alloy
kubectl apply -f alloy-test.yaml
kubectl get pod
kubectl logs -f {pod name}
```
佈署成功後應該在 `logs` 可以看到類似的資訊

```bash
🚀 Starting OTEL continuous sender container...
📦 Installing OpenTelemetry dependencies...
WARNING: Running pip as the 'root' user can result in broken permissions and conflicting behaviour with the system package manager. It is recommended to use a virtual environment instead: https://pip.pypa.io/warnings/venv
✅ Dependencies installed successfully
🔄 Starting continuous OTEL sender (runs indefinitely)...
🚀 Starting OTEL Continuous Sender...
Python version: 3.9.22 (main, Apr 30 2025, 00:01:18) 
[GCC 12.2.0]
📦 Importing OpenTelemetry modules...
✅ All modules imported successfully
🎯 Target: http://my-alloy-external.monitoring.svc.cluster.local:4317
🏷️  Service: python-otel-continuous-sender
🏠 Pod: otel-alloy-test-python-grpc-pod (Namespace: monitoring)
✅ Resource created
🔢 Setting up Metrics...
✅ Metrics setup complete
🔍 Setting up Traces...
✅ Traces setup complete
📝 Setting up Logs...
✅ Logs setup complete

🎉 All telemetry providers initialized successfully!
🔄 Starting continuous loop (every 5 seconds)...
💡 Press Ctrl+C to stop

✅ Loop 1: delete_item [200] (183.5ms)
❌ Loop 2: get_user [500] (459.7ms)
⚠️ Loop 3: get_user [404] (91.4ms)
✅ Loop 4: update_profile [201] (253.7ms)
⚠️ Loop 5: update_profile [404] (293.1ms)
⚠️ Loop 6: search_products [404] (286.5ms)
✅ Loop 7: search_products [200] (270.8ms)
```

1. 開啟 Alloy Live Debugging 確認是否有接收到資料
2. 在 Grafana 上確認是否有接收到資料
  * Mimir 
    * 查詢以下 metrics name
      * otel_continuous_requests_total
      * otel_continuous_active_operations
  * Loki
    * 查詢 app=otel-alloy-test-python-grpc
  * Tempo
    * 可以直接在面板上看到資料

## 透過 GitOps 來佈署你的系統

* ArgoCD (就是那隻章魚)
* Kustomize

### 為什麼需要 ArgoCD

ArgoCD 是一個宣告式的 GitOps 持續交付工具，專為 Kubernetes 設計。選擇 ArgoCD 的主要原因：

1. **GitOps 工作流程**：
   - 使用 Git 作為單一真相來源
   - 所有 Kubernetes 資源配置都存儲在 Git 倉庫中
   - 自動同步 Git 倉庫和叢集狀態

2. **自動化部署**：
   - 自動檢測應用程序配置更改
   - 持續監控應用程序狀態
   - 自動修正偏差（如果叢集狀態與 Git 中定義的期望狀態不符）

3. **多叢集管理**：
   - 可以管理多個 Kubernetes 叢集
   - 統一的部署流程和配置管理
   - 降低營運複雜度

### 安裝 ArgoCD

1. 首先，創建 ArgoCD 的命名空間：
```bash
kubectl create namespace argocd
kubens argocd
```

2. 部署 ArgoCD 的所有組件：
```bash
helm repo add argo https://argoproj.github.io/argo-helm
helm repo update

# ./argocd/helm
helm install argocd argo/argo-cd --namespace argocd -f values.yaml
```

3. 驗證安裝：
```bash
kubectl get pods -n argocd
```

4. 獲取初始管理員密碼：
```bash
# MacOS
kubectl -n argocd get secret argocd-initial-admin-secret \
          -o jsonpath="{.data.password}" | base64 -d; echo

# Windows Terminal
kubectl -n argocd get secret argocd-initial-admin-secret -o jsonpath="{.data.password}" | ForEach-Object { [System.Text.Encoding]::UTF8.GetString([System.Convert]::FromBase64String($_)) }
```

5. 設定訪問方式（選擇以下其中一種）：
   
   a. 通過 Port Forwarding：
   ```bash
   kubectl port-forward svc/argocd-server -n argocd 8080:443
   ```
   
   b. 通過 Ingress：
   在使用 helm 安裝 argocd 時就已經設定好了 ingress，現在可以使用瀏覽器開啟 `https://argocd.lab.dev:8443`

### Kustomize 與 ArgoCD 整合

Kustomize 是 Kubernetes 原生的配置管理工具，它允許你在不修改原始 YAML 檔案的情況下自定義 Kubernetes 資源。與 ArgoCD 結合使用，Kustomize 為 GitOps 流程提供了更強大的配置管理能力。

#### Kustomize 基本概念

1. **無模板設計**：
   - 不同於 Helm 的模板引擎，Kustomize 使用疊加方式管理配置
   - 基於 Kubernetes 資源的原始 YAML 檔案進行定制
   - 不需要學習新的模板語法

2. **核心組件**：
   - `kustomization.yaml` - 定義如何疊加和修改資源的主配置檔
   - `base` - 包含基礎資源定義的目錄
   - `overlays` - 包含各環境特定配置的目錄

3. **主要功能**：
   - 資源生成與組合
   - 跨環境配置管理（開發、測試、生產）
   - 補丁和變數覆蓋
   - 配置共享與繼承

#### Kustomize 目錄結構

```
my-app/
├── base/                     # 基礎配置
│   ├── deployment.yaml
│   ├── service.yaml
│   └── kustomization.yaml    # 基礎 kustomization
└── overlays/                 # 環境特定配置
    ├── dev/
    │   ├── kustomization.yaml  # 開發環境配置
    │   └── patch.yaml # 開發特定補丁
    ├── staging/
    │   ├── kustomization.yaml  # 測試環境配置
    │   └── patch.yaml # 測試特定補丁
    └── production/
        ├── kustomization.yaml  # 生產環境配置
        └── patch.yaml # 生產特定補丁
```

#### Kustomize 的疊加 (Overlay) 機制如何運作

1.  **`base` - 基礎配置的基石**：
    這個目錄存放了您應用程式最核心、通用的 Kubernetes 資源定義 (例如 `deployment.yaml`, `service.yaml`) 以及一個基礎的 `kustomization.yaml`。這些檔案構成了您應用程式部署的「藍圖」，不包含任何特定環境的細節。可以將其視為一個可重用的、標準化的組件。

2.  **`overlays` - 環境差異化的魔法**：
    此目錄專門用來管理不同環境（如 `dev` 開發環境、`staging` 預生產環境、`production` 生產環境）的特定配置。`overlays` 下的每一個子資料夾（例如 `overlays/production`）都會有其專屬的 `kustomization.yaml`。這個 `kustomization.yaml` 會明確指出它基於哪個 `base`，並定義如何「疊加」或「修改」這些基礎資源以適應當前環境。

想像一下，您正在為「生產環境」打包應用程式。當執行 `kustomize build overlays/production` 時，Kustomize 的運作流程如下：

*   **載入基礎 (Base)**：首先，Kustomize 會讀取並解析 `base` 目錄中定義的所有 Kubernetes 資源。
*   **應用疊加 (Apply Overlay)**：接著，它會根據 `overlays/production/kustomization.yaml` 中的指示，對這些從 `base` 載入的資源進行精確的調整。這些調整是「聲明式」的，意味著您只描述「期望的狀態」，而非「如何達成」。常見的疊加操作包括：
    *   **資源生成與組合 (Resource Generation and Combination)**：可以根據需要生成新的 Deployment, Service, ConfigMap、Secret，或組合多個小的 YAML 片段。
    *   **補丁 (Patches)**：這是 Kustomize 強大的功能之一。您可以透過 `patchesStrategicMerge` 進行策略性的合併，或者使用 `patchesJson6902` 進行基於 RFC 6902 JSON Patch 標準的精確欄位修改。例如，您可以：
        *   更改 Deployment 的副本數 (`spec/replicas`)。
        *   替換容器的映像檔標籤 (`spec/template/spec/containers/0/image`)。
        *   添加或修改環境變數 (`spec/template/spec/containers/0/env`)。
        *   調整資源請求與限制 (`spec/template/spec/containers/0/resources`)。
        *   添加特定的標籤 (labels) 或註解 (annotations)。
    *   **配置共享與繼承 (Configuration Sharing and Inheritance)**：`base` 的設計天然支持配置共享，而 `overlays` 則實現了差異化繼承。

#### Kustomize JSON 6902 Patches 使用說明

JSON 6902 patches 是 Kustomize 提供的精確修改 Kubernetes 資源的方法，基於 [RFC 6902](https://datatracker.ietf.org/doc/html/rfc6902) JSON Patch 標準。

#### 基本結構

```yaml
apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

patchesJson6902:
- target:
    group: apps        # API 群組
    version: v1        # API 版本
    kind: Deployment   # 資源類型
    name: demo-api     # 資源名稱
  patch: |-
    - op: replace
      path: /spec/replicas
      value: 2
```

#### 為什麼需要 Group 和 Version？

在 Kubernetes 中，每個資源都屬於特定的 API 群組和版本。完整的資源識別需要：

1. **API Group (群組)** - 資源所屬的 API 群組
2. **API Version (版本)** - API 的版本號
3. **Kind (資源類型)** - 資源的類型名稱
4. **Name (資源名稱)** - 資源的實例名稱

常見的 API Groups：

| 資源類型 | Group | Version | 說明 |
|---------|-------|---------|------|
| Deployment | apps | v1 | 應用程式部署 |
| StatefulSet | apps | v1 | 有狀態應用 |
| DaemonSet | apps | v1 | 守護程式集 |
| Service | "" (core) | v1 | 核心 API，group 使用空字串 |
| Pod | "" (core) | v1 | 核心 API |
| ConfigMap | "" (core) | v1 | 核心 API |
| Ingress | networking.k8s.io | v1 | 網路相關 |
| Job | batch | v1 | 批次作業 |
| CronJob | batch | v1 | 定時作業 |

#### JSON Patch 操作類型

1. **add** - 添加新的欄位或陣列元素
```yaml
- op: add
  path: /spec/template/spec/containers/0/env
  value:
    - name: ENVIRONMENT
      value: share
```

2. **replace** - 替換現有的值
```yaml
- op: replace
  path: /spec/replicas
  value: 3
```

3. **remove** - 刪除欄位或陣列元素
```yaml
- op: remove
  path: /spec/template/spec/containers/0/env/0
```

4. **copy** - 複製值到新位置
```yaml
- op: copy
  from: /spec/template/spec/containers/0
  path: /spec/template/spec/containers/1
```

5. **move** - 移動值到新位置
```yaml
- op: move
  from: /spec/template/spec/containers/0
  path: /spec/template/spec/containers/1
```

6. **test** - 測試值是否符合預期
```yaml
- op: test
  path: /spec/replicas
  value: 1
```

#### 路徑語法

JSON path 使用斜線分隔的路徑表示法：
- `/spec/replicas` - 訪問 spec 下的 replicas
- `/spec/template/spec/containers/0` - 訪問第一個容器（陣列索引從 0 開始）
- `/metadata/labels/app` - 訪問 labels 中的 app 標籤
- 使用 `~1` 來轉義路徑中的 `/` 字符
- 使用 `~0` 來轉義路徑中的 `~` 字符
- 使用 `/-` 在陣列末尾添加元素

#### 實際應用範例

1. **修改 Deployment 配置**
```yaml
patchesJson6902:
- target:
    group: apps
    version: v1
    kind: Deployment
    name: demo-api
  patch: |-
    # 修改副本數
    - op: replace
      path: /spec/replicas
      value: 2
    
    # 添加環境變數
    - op: add
      path: /spec/template/spec/containers/0/env
      value:
        - name: ENVIRONMENT
          value: share
        - name: LOG_LEVEL
          value: info
    
    # 修改資源限制
    - op: replace
      path: /spec/template/spec/containers/0/resources/limits/memory
      value: 256Mi
    
    # 添加標籤
    - op: add
      path: /metadata/labels/team
      value: platform
```

2. **修改 Service 配置**
```yaml
patchesJson6902:
- target:
    group: ""  # 核心 API 使用空字串
    version: v1
    kind: Service
    name: demo-service
  patch: |-
    - op: add
      path: /spec/sessionAffinity
      value: ClientIP
    
    - op: replace
      path: /spec/ports/0/port
      value: 8080
```

3. **修改 Ingress 配置**
```yaml
patchesJson6902:
- target:
    group: networking.k8s.io
    version: v1
    kind: Ingress
    name: demo-ingress
  patch: |-
    # 添加註解（注意路徑中的特殊字符處理）
    - op: add
      path: /metadata/annotations/nginx.ingress.kubernetes.io~1rewrite-target
      value: "/$1"
```

#### 處理複雜資料結構

當需要替換複雜的資料結構時，可以直接在 value 中提供完整的物件或陣列：

```yaml
patchesJson6902:
- target:
    group: apps
    version: v1
    kind: Deployment
    name: demo-api
  patch: |-
    # 替換整個容器陣列
    - op: replace
      path: /spec/template/spec/containers
      value:
        - name: main-app
          image: myapp:latest
          ports:
            - containerPort: 8080
        - name: sidecar
          image: sidecar:latest
          ports:
            - containerPort: 9090
```

#### 最佳實踐

1. **使用精確的路徑** - 避免意外修改其他欄位
2. **測試修補結果** - 使用 `kustomize build` 預覽變更
3. **保持修補簡潔** - 每個修補專注於單一目的
4. **添加註釋** - 說明每個修補的用途

###### 除錯技巧

1. 預覽生成的資源：
```bash
kustomize build overlays/share
```

2. 驗證特定資源的修補結果：
```bash
kustomize build overlays/share | kubectl get -f - -o yaml --dry-run=client
```

3. 常見錯誤：
- 路徑錯誤：確保路徑存在且拼寫正確
- 類型不匹配：確保 value 的類型與目標欄位匹配
- Group/Version 錯誤：使用 `kubectl api-resources` 確認正確的值



#### 在 ArgoCD 中使用 Kustomize

ArgoCD 原生支援 Kustomize，可以輕鬆整合到 GitOps 工作流程：

1. **透過 UI 創建 Kustomize 應用**:
   - 選擇 `New App` > `SYNC POLICY` > `SYNC OPTIONS` > 勾選 `Use Kustomize`
   - 指定儲存庫 URL、路徑（指向包含 kustomization.yaml 的目錄）
   - 選擇環境對應的覆蓋層（如 `overlays/production`）

2. **在 ArgoCD 應用清單（Application Manifest）中設定**:
```yaml
apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: my-app
  namespace: argocd
spec:
  project: default
  source:
    repoURL: https://github.com/your-org/your-repo.git
    targetRevision: HEAD
    path: overlays/production
  destination:
    server: https://kubernetes.default.svc
    namespace: production
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
```

### 安裝 ArgoCD CLI

1. 不同平台安裝方式
* 在 macOS 上使用 Homebrew 安裝：
  ```bash
  brew install argocd
  ```
* 在 Windows 直接在 https://github.com/argoproj/argo-cd/releases 下載 `argocd-windows-amd64.exe`

2. 驗證安裝：
```bash
argocd version
```

### 通過 ArgoCD CLI 設定 Git Repository

1. 登入 ArgoCD：
```bash
argocd login <ARGOCD_SERVER>
```

2. 添加私有 Git 倉庫：

a. 使用用戶名和密碼：
```bash
argocd repo add https://github.com/your-org/your-repo.git \
  --username <GIT_USERNAME> \
  --password <GIT_PASSWORD>
```

b. 使用 Access Token（推薦）：
```bash
argocd repo add https://github.com/jamisliao/kind-lab.git \
  --username jamisliao \
  --password <YOUR_ACCESS_TOKEN>
```

建議的 Access Token 權限設定：
- 僅配置必要的最小權限：
  - `repo` - 倉庫的讀取權限
  - `read:org` - 如果需要訪問組織倉庫
- 建議針對特定倉庫設定 Token 權限
- 建議定期輪換 Token 以增加安全性

3. 驗證倉庫連接狀態：
```bash
argocd repo list
```

4. 創建應用程序：
```bash
argocd app create my-app \
  --repo https://github.com/your-org/your-repo.git \
  --path overlays/production \
  --dest-server https://kubernetes.default.svc \
  --dest-namespace production \
  --sync-policy automated
```

5. 同步應用程序：
```bash
argocd app sync my-app
```

#### 使用 Kustomize 與 ArgoCD 的最佳實踐

1. **標準化目錄結構**:
   - 使用一致的 base/overlays 結構
   - 每個應用或服務保持相同的結構

2. **避免重複配置**:
   - 盡可能在基礎層定義共享配置
   - 只在覆蓋層定義環境特定的變更

3. **有效使用補丁**:
   - 使用 patchesStrategicMerge 進行小範圍修改
   - 使用 patchesJson6902 進行更精確的 JSON 補丁

4. **版本控制策略**:
   - 將所有環境的 Kustomize 配置保存在同一儲存庫中
   - 使用分支或標籤進行配置版本管理

5. **CI/CD 整合**:
   - 在合併前自動驗證 Kustomize 配置
   - 使用 `kustomize build` 預覽變更影響

#### 故障排除

1. 檢查 Kustomize 生成的資源：
```bash
# ./argocd/kustomize/overlays/share
kubectl apply -f .
kustomize build .
```

2. 驗證 ArgoCD 應用程式同步狀態：
```bash
argocd app get my-app
```
3. 常見問題：
   - 路徑設定錯誤：確保 path 指向包含 kustomization.yaml 的目錄
   - 資源衝突：檢查命名空間和資源命名
   - 補丁不生效：確認資源名稱和選擇器正確匹配

## Vault

HashiCorp Vault 是一個用於管理敏感資料的工具，在 Kubernetes 環境中主要用於：
- 集中管理和存儲敏感資訊（密碼、API 金鑰、證書等）
- 動態生成臨時憑證
- 強化安全性與存取控制

### 安裝 Vault

```bash
kubectl create namespace resource
kubens resource
helm repo add hashicorp https://helm.releases.hashicorp.com
helm install vault hashicorp/vault -f values.yaml -n resource
```

### vault operator init
```bash
kubectl exec -it vault-0 -- sh
vault operator init
 ```

你會看到
```bash
Unseal Key 1: 7HEZMeeSA3R1ier8n4wSq7VGiVFNl5n+JbLeQEf7Sv80
Unseal Key 2: TqaqcVaWUcHyr2O6+k7sDQqOizVX3haCV9gNWA6mcq+J
Unseal Key 3: No+pFRXhXowMZUiVSQyK34UnbayKKo8csfP4UNISA/qh
Unseal Key 4: NoYPaYuSeCJ8f4tkQqDu7htwUaqqP9lg5DYXvoMg7d0s
Unseal Key 5: 6uiFqdWCavxPZW5oUWUc04grGvjOOp/nfl+72CqwTYF5

Initial Root Token: hvs.1wHA48pqP8NhOZR74o8qHbzs
```

使用
```bash
vault operator unseal <Token1>
vault operator unseal <Token2>
vault operator unseal <Token3>
```

成功 unseal 後你會看到
```bash
Key             Value
---             -----
Seal Type       shamir
Initialized     true
Sealed          false
Total Shares    5
Threshold       3
Version         1.19.0
Build Date      2025-03-04T12:36:40Z
Storage Type    file
Cluster Name    vault-cluster-c5f07045
Cluster ID      3716c8a4-ee65-9c46-ef39-6251e0a8a8dd
HA Enabled      false
```

這個時候就可以使用
```bash
vault login <root.token>
```   

### 設定 Kubernetes 認證

1. 啟用 Kubernetes 認證方式：
```bash
vault auth enable kubernetes
```
你會看到
```bash
Success! Enabled kubernetes auth method at: kubernetes/
```

2. 設定 Kubernetes 認證配置：
```bash
vault write auth/kubernetes/config \
  token_reviewer_jwt="$(cat /var/run/secrets/kubernetes.io/serviceaccount/token)" \
  kubernetes_host=https://${KUBERNETES_PORT_443_TCP_ADDR}:443 \
  kubernetes_ca_cert=@/var/run/secrets/kubernetes.io/serviceaccount/ca.crt
```

你會看到
```bash
Success! Data written to: auth/kubernetes/config
```

此配置允許 Vault 與 Kubernetes API 進行通信，驗證 Pod 的服務帳號。

### 設定訪問策略

1. 創建策略定義允許的操作：
```bash
vault policy write vault-sa-policy - <<EOF
path "kv/*" {
  capabilities = ["read"]
}
EOF
```
你會看到
```bash
Success! Uploaded policy: vault-sa-policy
```

此策略允許讀取 `kv/` 路徑下的所有內容。


### 創建角色

1. 設定 Kubernetes 角色：
```bash
vault write auth/kubernetes/role/vault-sa \
    bound_service_account_names=default \
    bound_service_account_namespaces=sharewithvault \
    policies=vault-sa-policy
```

你會看到
```bash
Success! Data written to: auth/kubernetes/role/vault-sa
```

參數說明：
- `bound_service_account_names`：允許使用此角色的服務帳號名稱
- `bound_service_account_namespaces`：允許的命名空間
- `policies`：套用的存取策略

### 啟用 KV (Version 2)

```bash
vault secrets enable -version=2 kv
```

使用 `vault secrets list` 確認

使用以下指令來新增 Secrets
```bash
vault kv put kv/share/demo username=admin password=secret
```

新增完畢後可以使用 `vault kv get kv/share/demo` 來確認是否新增成功，正常會看到

```bash
=== Secret Path ===
kv/data/share/demo

======= Metadata =======
Key                Value
---                -----
created_time       2025-04-26T04:54:09.153088705Z
custom_metadata    <nil>
deletion_time      n/a
destroyed          false
version            1

====== Data ======
Key         Value
---         -----
password    secret
username    admin
```

### 在 Pod 中使用 Vault

要在 Pod 裡使用 Vault 上的資料主要有兩種方式：
* Vault Agent Injector
* Vault Secrets Operator

#### Vault Agent Injector

Vault Agent Injector 是一個 Kubernetes Mutation Webhook Controller，它能夠：
- 攔截 Pod 的建立和更新事件
- 自動注入 Vault Agent 容器
- 將密鑰渲染到共享記憶體卷中
- 讓容器無需知道 Vault 的存在即可使用密鑰

#### 1. 基本配置

在 Pod 規格中添加服務帳號：
```yaml
apiVersion: v1
kind: Deployment
metadata:
  name: demo-api
  annotations:
    vault.hashicorp.com/agent-inject: "true"            # 啟用 Vault Agent 注入
    vault.hashicorp.com/agent-inject-status: "update"   # 
    vault.hashicorp.com/role: "vault-sa"                # 指定 Vault 角色
spec:
  serviceAccountName: default  # 使用已配置的服務帳號
  containers:
  - name: demo-container
    image: ghcr.io/jamisliao/lab/demo:*******
```

透過 `annotations` 配置注入 vault secret：

使用註解（簡單方式）：
```yaml
annotations:
  # 指定輸出格式（例如：foo.txt）
  vault.hashicorp.com/agent-inject-secret-demo-creds.txt: "kv/data/share/demo"
  
  # 自定義模板
  vault.hashicorp.com/agent-inject-template-demo-creds.txt: |
    {{- with secret "kv/data/share/demo" -}}
    username: {{ .Data.data.username }}
    password: {{ .Data.data.password }}
    {{ end }}
```

使用 argocd 佈署 `argocd/kustomize/overlays/sharewithvault` 的應用程式

透過上述的方式，所有注入的密鑰都會被掛載到 `/vault/secrets/` 目錄：
```bash
kubectl exec -it {demo pai pod name} -- sh
# 預設模板格式
$ cat /vault/secrets/demo-creds.txt
username: my-user
password: my-password
```

#### Vault Secrets Operator (VSO)

Vault Secrets Operator (VSO) 是 HashiCorp 官方提供的 Kubernetes 操作器，用於將 Vault 中的機密資料同步到 Kubernetes Secret 資源。VSO 提供比 Vault Agent Injector 更多的靈活性和功能。

#### 1. 安裝 VSO

```bash
# MacOS
helm install vault-secrets-operator hashicorp/vault-secrets-operator \
  --namespace resource \
  --version 0.10.0

# Windows Terminal
helm install vault-secrets-operator hashicorp/vault-secrets-operator `
  --namespace resource `
  --version 0.10.0
```

#### 2. 配置 Vault 連接

##### 創建 VaultConnection 資源

首先，需要建立一個 `VaultConnection` 資源來連接到 Vault 服務：

```yaml
apiVersion: secrets.hashicorp.com/v1beta1
kind: VaultConnection
metadata:
  name: vault-connection
spec:
  # Vault 服務地址
  address: http://vault.resource.svc:8200
  # 測試環境可跳過 TLS 驗證，生產環境應配置適當的 TLS
  skipTLSVerify: true
```

佈署 VaultConnection
```bash
kubectl apply -f vault-connection.yaml
kubectl get VaultConnection
kubectl describe VaultConnection vault-connection
```

#### 3. 設置 Vault 認證方式

##### 配置 VaultAuth 資源

設定 Kubernetes 認證方法，允許 VSO 使用 Kubernetes 服務帳號認證到 Vault：

```yaml
apiVersion: secrets.hashicorp.com/v1beta1
kind: VaultAuth
metadata:
  name: vault-auth
spec:
  # 引用之前創建的 VaultConnection
  vaultConnectionRef: vault-connection
  # 使用 Kubernetes 認證方式
  method: kubernetes
  # Kubernetes 認證的配置
  kubernetes:
    # Vault 中配置的角色名稱
    role: vault-sa
    # 掛載點，默認為 "kubernetes"
    mountPath: kubernetes
    # 指定 Service Account
    serviceAccount: default
```

佈署 VaultAuth
```bash
kubectl apply -f vault-auth.yaml
kubectl get VaultAuth
kubectl describe VaultAuth vault-auth-for-demo
```

#### 4. 同步 Vault 機密到 Kubernetes Secret

##### 4.1 同步靜態機密

通過 `VaultStaticSecret` 資源同步靜態機密：

```yaml
apiVersion: secrets.hashicorp.com/v1beta1
kind: VaultStaticSecret
metadata:
  name: vault-secret
  namespace: share
spec:
  # 引用之前創建的 VaultAuth
  vaultAuthRef: vault-auth-for-demo
  # 目標 Namespace，默認為資源所在 Namespace
  destination:
    # 創建的 Kubernetes Secret 名稱
    name: share-demo
  # 機密路徑
  mount: kv
  path: data/share/demo
  # 同步的時間間隔（可選）
  refreshAfter: 60s
  # 指定要同步的鍵值對
  secrets:
    - key: username
      name: VAULT_USER
    - key: password
      name: VAULT_PASSWORD
```

佈署 VaultStaticSecret
```bash
kubectl apply -f vault-secret.yaml
kubectl get VaultStaticSecret
kubectl describe VaultStaticSecret vault-secret
kubectl get secret
kubectl describe secret share-demo
```

#### 5. 在 Pod 中使用同步的機密

##### 作為環境變量

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: demo-vault-operator
spec:
  template:
    spec:
      containers:
      - name: demo
        image: ghcr.io/jamisliao/lab/demo:*******
        env:
        - name: VAULT_USERNAME
          valueFrom:
            secretKeyRef:
              name: share-demo
              key: username
        - name: VAULT_PASSWORD
          valueFrom:
            secretKeyRef:
              name: share-demo
              key: password
```

使用 `./vault/demo-deployment.yaml` 佈署 Pod 來驗證，進入到 Pod 後，使用
```bash
# /vault/vso
kubectl apply -f demo-deployment.yaml

# demo-vault-operator-xxxxx-xxxxx
kubectl exec -it {pod name} -- sh
env
```

可以看到
```bash
DEMO_API_SERVICE_SERVICE_HOST=***********
KUBERNETES_SERVICE_PORT=443
KUBERNETES_PORT=tcp://*********:443
HOSTNAME=demo-vault-operator-7669fdf744-4db5s
HOME=/home/<USER>
DOTNET_RUNNING_IN_CONTAINER=true
DEMO_API_SERVICE_SERVICE_PORT=80
DEMO_API_SERVICE_PORT=tcp://***********:80
DOTNET_VERSION=8.0.14
DB_USERNAME=admin  # username
DEMO_API_SERVICE_PORT_80_TCP_ADDR=***********
DEMO_API_SERVICE_PORT_80_TCP_PORT=80
DEMO_API_SERVICE_PORT_80_TCP_PROTO=tcp
TERM=xterm
KUBERNETES_PORT_443_TCP_ADDR=*********
PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin
KUBERNETES_PORT_443_TCP_PORT=443
ASPNETCORE_HTTP_PORTS=8080
KUBERNETES_PORT_443_TCP_PROTO=tcp
APP_UID=1654
DEMO_API_SERVICE_PORT_80_TCP=tcp://***********:80
KUBERNETES_SERVICE_PORT_HTTPS=443
KUBERNETES_PORT_443_TCP=tcp://*********:443
DB_PASSWORD=secret   # password
KUBERNETES_SERVICE_HOST=*********
PWD=/app
ASPNET_VERSION=8.0.14
```

#### 6. VSO vs Vault Agent Injector

| 特性 | Vault Secrets Operator | Vault Agent Injector |
|-----|------------------------|----------------------|
| 實現方式 | 將 Vault 機密同步到 K8s Secret | 直接將機密注入到 Pod 中 |
| 適用場景 | 需要 K8s Secret 原生整合的場景 | 只需在 Pod 內使用機密的場景 |
| 機密更新 | 定期自動同步，支持自動重啟 Pod | 支持自動更新，無需重啟 Pod |
| 配置方式 | 使用 CRD 資源定義 | 使用 Pod 註解 (annotations) |
| 安全性考量 | 機密存儲在 etcd 中 | 機密只存在於 Pod 的內存中 |

