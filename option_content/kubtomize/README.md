# Kustomize 技巧範例集合 (合併優化版)

本目錄包含從 lab 的 Kustomize 實踐中提取的各種進階技巧範例，已整合為更易學習和使用的結構。

## 📁 目錄結構（優化後）

### 01-comprehensive-base-overlays - 綜合 Base/Overlays 模式
展示完整的 Kustomize 模式，包含基本部署、多租戶配置和角色管理。

```
01-comprehensive-base-overlays/
├── base/                           # 基礎配置
│   ├── deployment.yaml
│   ├── service.yaml
│   ├── serviceaccount.yaml
│   ├── workflowtemplate.yaml
│   └── kafka/
├── overlays/                       # 環境特定配置
│   ├── dev/
│   └── sit/
├── teams/                          # 多租戶配置
│   ├── skilltree/
│   ├── sub1/
│   └── sub2/
# 角色型配置已移除
```

**特點：**
- ✅ Base/Overlays 經典模式
- ✅ 多租戶隔離（namespace + namePrefix）
- ✅ 環境差異化配置

**執行命令：**
```bash
# 基本環境部署
kubectl apply -k 01-comprehensive-base-overlays/overlays/dev
kubectl apply -k 01-comprehensive-base-overlays/overlays/sit

# 團隊配置部署
kubectl apply -k 01-comprehensive-base-overlays/teams/skilltree
kubectl apply -k 01-comprehensive-base-overlays/teams/sub1
kubectl apply -k 01-comprehensive-base-overlays/teams/sub2
```

### 02-images-override - Images 標籤覆寫
統一管理多個容器映像的標籤版本，適合 CI/CD 集成。

**執行命令：**
```bash
kustomize build 02-images-override
```

### 03-patch-techniques - Patch 技巧集合
整合三種主要的 patch 技巧，適合不同場景的配置修改。

```
03-patch-techniques/
├── inline-patches/                 # 簡單的 inline JSON patches
├── strategic-merge/                # 複雜的 YAML 合併 patches
└── json-patches/                   # 精準的 JSON 路徑操作
```

**特點：**
- ✅ Inline Patches：適合簡單變更
- ✅ Strategic Merge：適合複雜配置（如 Vault 注入）
- ✅ JSON Patches：適合精準參數修改

**執行命令：**
```bash
# 測試各種 patch 技巧
kustomize build 03-patch-techniques/inline-patches
kustomize build 03-patch-techniques/strategic-merge
kustomize build 03-patch-techniques/json-patches
```

### 04-complex-composition - 組合式配置
展示 Components 功能的進階用法，包括模組化配置組合。

**執行命令：**
```bash
kubectl apply -k 04-complex-composition/environments/production
```

## 🚀 快速開始

### 1. 驗證所有範例
```bash
./validate.sh
```

### 2. 部署特定範例
```bash
./deploy.sh <範例名稱> [動作]
```

### 3. 支援的範例名稱
- `comprehensive-dev` - 綜合範例開發環境
- `comprehensive-sit` - 綜合範例 SIT 環境  
- `team-skilltree` - skilltree 團隊配置
- `team-sub1` - sub1 團隊配置
- `team-sub2` - sub2 團隊配置
# 角色型配置已移除
- `images-override` - 映像標籤覆寫
- `patch-inline` - Inline patches
- `patch-strategic` - Strategic merge patches
- `patch-json` - JSON patches
- `complex-composition` - 複雜組合配置

## 📚 學習路徑

### 🥉 初級（從這裡開始）
1. **基本 Base/Overlays 模式**
   ```bash
   kustomize build 01-comprehensive-base-overlays/overlays/dev
   ```

2. **映像標籤管理**
   ```bash
   kustomize build 02-images-override
   ```

### 🥈 中級
3. **簡單 Patch 技巧**
   ```bash
   kustomize build 03-patch-techniques/inline-patches
   ```

4. **多租戶配置**
   ```bash
   kustomize build 01-comprehensive-base-overlays/teams/skilltree
   ```

### 🥇 高級
5. **複雜 Patch 技巧**
   ```bash
   kustomize build 03-patch-techniques/strategic-merge
   kustomize build 03-patch-techniques/json-patches
   ```

# 角色型權限管理已移除

7. **組合式配置**
   ```bash
   kustomize build 04-complex-composition/environments/production
   ```

## 🛠️ 技巧對照表

| 技巧 | 適用場景 | 複雜度 | 位置 |
|------|----------|--------|----- |
| Base/Overlays | 多環境部署 | 🟢 基礎 | 01-comprehensive/overlays/ |
| 多租戶配置 | 團隊隔離 | 🟡 中級 | 01-comprehensive/teams/ |

| Images Override | CI/CD 集成 | 🟢 基礎 | 02-images-override/ |
| Inline Patches | 簡單變更 | 🟢 基礎 | 03-patch-techniques/inline/ |
| Strategic Merge | 複雜變更 | 🟡 中級 | 03-patch-techniques/strategic/ |
| JSON Patches | 精準變更 | 🔴 高級 | 03-patch-techniques/json/ |
| Components | 模組化配置 | 🔴 高級 | 04-complex-composition/ |

## 📖 最佳實踐

### 🎯 配置原則
1. **分層設計**：base → environment/team → specific
2. **命名規範**：使用 namePrefix 避免衝突
3. **標籤管理**：使用 commonLabels 統一標記
4. **權限隔離**：每個團隊使用獨立的 ServiceAccount

### ⚡ 效率提升
1. **統一映像管理**：使用 images 字段批量管理版本
2. **參數化配置**：使用 replacements 和 patches
3. **模組化設計**：使用 components 提高複用性
4. **自動化驗證**：使用 validate.sh 檢查配置

### 🔒 安全建議
1. **密鑰管理**：整合 Vault 或其他密鑰管理系統
2. **權限最小化**：為每個團隊分配最小必要權限
3. **網路隔離**：使用 namespace 進行網路隔離
4. **資源限制**：為每個環境設定適當的資源配額

## 🔧 工具支援

### 驗證工具
```bash
# 語法驗證
./validate.sh

# 乾運行（不部署）
./deploy.sh <範例名稱> build

# 差異比較
./deploy.sh <範例名稱> diff
```

### 偵錯技巧
```bash
# 查看最終生成的 YAML
kustomize build <目錄> > output.yaml

# 驗證資源
kubectl apply --dry-run=client -k <目錄>

# 檢查資源狀態
kubectl get -k <目錄>
```

## 📈 從舊版本遷移

如果您使用的是舊版本的範例結構，請參考以下對照表：

| 舊範例 | 新位置 | 說明 |
|--------|--------|------|
| 01-base-overlays | 01-comprehensive-base-overlays/overlays/ | 已整合到綜合範例 |
| 03-inline-patches | 03-patch-techniques/inline-patches/ | 已合併到 patch 技巧集合 |
| 04-vault-injection | 03-patch-techniques/strategic-merge/ | 已合併到 patch 技巧集合 |
| 05-multi-tenant | 01-comprehensive-base-overlays/teams/ | 已整合到綜合範例 |
| 06-precise-parameter | 03-patch-techniques/json-patches/ | 已合併到 patch 技巧集合 |
| 07-role-based-sa | 已移除 | 角色型權限配置已移除 |
| 08-complex-composition | 04-complex-composition/ | 重新編號 |

## 🤝 貢獻

歡迎提交 Issue 和 Pull Request 來改進這些範例！

## 📄 授權

這些範例僅用於學習和參考目的。

---

**快速導航：**
- [綜合 Base/Overlays 範例](./01-comprehensive-base-overlays/)
- [映像覆寫範例](./02-images-override/)  
- [Patch 技巧集合](./03-patch-techniques/)
- [複雜組合配置](./04-complex-composition/)
- [部署腳本](./deploy.sh)
- [驗證腳本](./validate.sh)
