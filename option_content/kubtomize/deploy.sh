#!/bin/bash

# Kustomize 範例快速部署腳本 (合併優化版)
# 用於快速部署特定範例到 Kubernetes 集群

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 顯示使用方法
show_usage() {
    echo "用法: $0 <範例名稱> [action]"
    echo ""
    echo "📚 範例名稱 (按學習難度排序):"
    echo ""
    echo "🟢 初級範例:"
    echo "  comprehensive-dev       - 綜合範例開發環境"
    echo "  comprehensive-sit       - 綜合範例 SIT 環境"
    echo "  images-override         - Images 標籤覆寫"
    echo "  patch-inline           - Inline Patches 技巧"
    echo ""
    echo "🟡 中級範例:"
    echo "  team-skilltree         - skilltree 團隊配置"
    echo "  team-sub1              - sub1 團隊配置"
    echo "  team-sub2              - sub2 團隊配置"
    echo "  patch-strategic        - Strategic Merge Patches"
    echo ""
    echo "🔴 高級範例:"
    echo "  patch-json             - JSON Patches 技巧"
    echo "  complex-composition    - 複雜組合配置"
    echo ""
    echo "🔧 動作 (可選):"
    echo "  apply    - 部署到集群 (默認)"
    echo "  build    - 只構建 YAML，不部署"
    echo "  delete   - 從集群刪除"
    echo "  diff     - 顯示與集群的差異"
    echo "  validate - 驗證配置語法"
    echo ""
    echo "📝 範例："
    echo "  $0 comprehensive-dev          # 部署綜合範例開發環境"
    echo "  $0 team-skilltree build       # 只構建 skilltree 團隊配置"
    echo "  $0 patch-strategic delete     # 刪除 Strategic Merge 配置"
    echo "  $0 complex-composition diff   # 查看複雜組合配置的差異"
    echo ""
    echo "💡 提示："
    echo "  - 建議按順序學習：初級 → 中級 → 高級"
    echo "  - 使用 validate 動作檢查配置語法"
    echo "  - 使用 build 動作預覽最終 YAML"
}

# 檢查依賴
check_dependencies() {
    local missing_deps=()
    
    if ! command -v kubectl &> /dev/null; then
        missing_deps+=("kubectl")
    fi
    
    if ! command -v kustomize &> /dev/null; then
        missing_deps+=("kustomize")
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        echo "❌ 錯誤: 以下工具未安裝或不在 PATH 中:"
        printf '   - %s\n' "${missing_deps[@]}"
        echo ""
        echo "💡 安裝建議:"
        echo "   kubectl: https://kubernetes.io/docs/tasks/tools/"
        echo "   kustomize: https://kubectl.docs.kubernetes.io/installation/kustomize/"
        echo "   或使用 kubectl 內建的 kustomize: kubectl apply -k ."
        exit 1
    fi
    
    # 檢查 kubectl 是否能連接到集群
    if [[ "$ACTION" =~ ^(apply|delete|diff)$ ]]; then
        if ! kubectl cluster-info &> /dev/null; then
            echo "⚠️  警告: 無法連接到 Kubernetes 集群"
            echo "請檢查您的 kubeconfig 配置"
            echo "當前 context: $(kubectl config current-context 2>/dev/null || echo '無')"
            echo ""
            read -p "是否繼續? (y/N): " -r
            if [[ ! $REPLY =~ ^[Yy]$ ]]; then
                echo "❌ 已取消操作"
                exit 1
            fi
        fi
    fi
}

# 映射範例名稱到目錄路徑
get_example_path() {
    local example_name="$1"
    
    case "$example_name" in
        # 綜合 Base/Overlays 範例
        "comprehensive-dev")
            echo "01-comprehensive-base-overlays/overlays/dev"
            ;;
        "comprehensive-sit")
            echo "01-comprehensive-base-overlays/overlays/sit"
            ;;
        
        # 團隊配置
        "team-skilltree")
            echo "01-comprehensive-base-overlays/teams/skilltree"
            ;;
        "team-sub1")
            echo "01-comprehensive-base-overlays/teams/sub1"
            ;;
        "team-sub2")
            echo "01-comprehensive-base-overlays/teams/sub2"
            ;;
            
        # 角色型配置已移除
            
        # Images 覆寫
        "images-override")
            echo "02-images-override"
            ;;
            
        # Patch 技巧
        "patch-inline")
            echo "03-patch-techniques/inline-patches"
            ;;
        "patch-strategic")
            echo "03-patch-techniques/strategic-merge"
            ;;
        "patch-json")
            echo "03-patch-techniques/json-patches"
            ;;
            
        # 複雜組合
        "complex-composition")
            echo "04-complex-composition/environments/production"
            ;;
            
        *)
            echo ""
            ;;
    esac
}

# 獲取範例的描述和難度
get_example_info() {
    local example_name="$1"
    
    case "$example_name" in
        "comprehensive-dev")
            echo "🟢 綜合範例開發環境 - Base/Overlays 基本模式"
            ;;
        "comprehensive-sit")
            echo "🟢 綜合範例 SIT 環境 - Base/Overlays 基本模式"
            ;;
        "team-skilltree")
            echo "🟡 skilltree 團隊配置 - 多租戶隔離"
            ;;
        "team-sub1")
            echo "🟡 sub1 團隊配置 - 多租戶隔離"
            ;;
        "team-sub2")
            echo "🟡 sub2 團隊配置 - 多租戶隔離"
            ;;
        # 角色型配置已移除
        "images-override")
            echo "🟢 Images 標籤覆寫 - CI/CD 集成"
            ;;
        "patch-inline")
            echo "🟢 Inline Patches - 簡單配置變更"
            ;;
        "patch-strategic")
            echo "🟡 Strategic Merge Patches - 複雜配置變更"
            ;;
        "patch-json")
            echo "🔴 JSON Patches - 精確路徑修改"
            ;;
        "complex-composition")
            echo "🔴 複雜組合配置 - Components 進階用法"
            ;;
        *)
            echo "❓ 未知範例"
            ;;
    esac
}

# 執行指定動作
execute_action() {
    local path="$1"
    local action="$2"
    local example_name="$3"
    local full_path="$SCRIPT_DIR/$path"
    
    echo "📂 範例路徑: $path"
    echo "📋 範例描述: $(get_example_info "$example_name")"
    echo "🔧 執行動作: $action"
    echo ""
    
    case "$action" in
        "build")
            echo "🔨 構建 YAML 配置..."
            if kustomize build "$full_path"; then
                echo "✅ 構建成功"
            else
                echo "❌ 構建失敗"
                exit 1
            fi
            ;;
        "validate")
            echo "🔍 驗證配置語法..."
            if kustomize build "$full_path" > /dev/null; then
                echo "✅ 配置語法正確"
            else
                echo "❌ 配置語法錯誤"
                exit 1
            fi
            ;;
        "apply")
            echo "🚀 部署到 Kubernetes 集群..."
            echo "🎯 目標集群: $(kubectl config current-context)"
            echo ""
            
            # 顯示將要部署的資源預覽
            echo "📋 將要部署的資源:"
            kustomize build "$full_path" | kubectl apply --dry-run=client -f - 2>/dev/null | grep -E "^(.*created|.*configured|.*unchanged)$" || true
            echo ""
            
            read -p "確認要部署嗎? (y/N): " -r
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                if kubectl apply -k "$full_path"; then
                    echo "✅ 部署完成"
                    
                    # 顯示部署狀態
                    echo ""
                    echo "📊 部署狀態:"
                    kubectl get -k "$full_path" 2>/dev/null || echo "⚠️  無法獲取資源狀態"
                else
                    echo "❌ 部署失敗"
                    exit 1
                fi
            else
                echo "❌ 部署已取消"
            fi
            ;;
        "delete")
            echo "🗑️  從 Kubernetes 集群刪除..."
            echo "🎯 目標集群: $(kubectl config current-context)"
            echo ""
            
            # 顯示將要刪除的資源
            echo "📋 將要刪除的資源:"
            kubectl get -k "$full_path" 2>/dev/null | head -10 || echo "⚠️  找不到相關資源"
            echo ""
            
            read -p "確認要刪除嗎? (y/N): " -r
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                if kubectl delete -k "$full_path"; then
                    echo "✅ 刪除完成"
                else
                    echo "❌ 刪除失敗"
                    exit 1
                fi
            else
                echo "❌ 刪除已取消"
            fi
            ;;
        "diff")
            echo "🔍 顯示與集群的差異..."
            
            # 檢查是否有 kubectl diff 插件
            if kubectl plugin list 2>/dev/null | grep -q "kubectl-diff"; then
                kubectl diff -k "$full_path"
            else
                # 使用基本方法比較
                echo "⚠️  注意: 未安裝 kubectl diff 插件，使用基本比較方法"
                echo ""
                
                local temp_new="/tmp/kustomize-new-$$.yaml"
                local temp_current="/tmp/kustomize-current-$$.yaml"
                
                echo "🔨 生成新配置..."
                kustomize build "$full_path" > "$temp_new"
                
                echo "📥 獲取集群當前狀態..."
                if kubectl get -k "$full_path" -o yaml > "$temp_current" 2>/dev/null; then
                    if command -v diff &> /dev/null; then
                        echo "📊 配置差異:"
                        diff -u "$temp_current" "$temp_new" || echo "📝 以上為配置差異"
                    else
                        echo "⚠️  無法比較差異，請安裝 diff 工具"
                    fi
                else
                    echo "ℹ️  集群中沒有相關資源，這將是新部署"
                fi
                
                # 清理臨時文件
                rm -f "$temp_new" "$temp_current"
            fi
            ;;
        *)
            echo "❌ 錯誤: 未知的動作 '$action'"
            show_usage
            exit 1
            ;;
    esac
}

# 顯示學習建議
show_learning_tips() {
    local example_name="$1"
    
    echo ""
    echo "💡 學習建議:"
    
    case "$example_name" in
        "comprehensive-dev"|"comprehensive-sit")
            echo "   - 這是最基礎的 Base/Overlays 模式"
            echo "   - 學習重點: 基礎配置共享, 環境差異化"
            echo "   - 建議下一步: 嘗試 images-override 或 team-* 範例"
            ;;
        "images-override")
            echo "   - 學習 CI/CD 集成的關鍵技巧"
            echo "   - 學習重點: 統一映像版本管理"
            echo "   - 建議下一步: 嘗試 patch-inline 範例"
            ;;
        "patch-inline")
            echo "   - 最簡單的 patch 技巧"
            echo "   - 學習重點: JSON Patch 基本操作"
            echo "   - 建議下一步: 嘗試 patch-strategic 範例"
            ;;
        "team-"*)
            echo "   - 多租戶隔離的核心技巧"
            echo "   - 學習重點: namespace 隔離, namePrefix 使用"
            echo "   - 建議下一步: 嘗試 patch-strategic 範例"
            ;;
        "patch-strategic")
            echo "   - 複雜配置變更的主要方法"
            echo "   - 學習重點: YAML 合併, Vault 集成"
            echo "   - 建議下一步: 嘗試 patch-json 範例"
            ;;
        "patch-json")
            echo "   - 最精確的配置修改方法"
            echo "   - 學習重點: JSON 路徑操作, 陣列處理"
            echo "   - 建議下一步: 嘗試 complex-composition 範例"
            ;;
        "complex-composition")
            echo "   - 最高級的 Kustomize 技巧"
            echo "   - 學習重點: Components 模組化, 條件組合"
            echo "   - 恭喜: 您已掌握所有主要的 Kustomize 技巧!"
            ;;
    esac
    
    echo ""
    echo "📚 相關文檔:"
    echo "   - 總覽: cat README.md"
    echo "   - 架構: cat ARCHITECTURE.md" 
    if [[ "$example_name" =~ ^patch- ]]; then
        echo "   - Patch 技巧: cat 03-patch-techniques/README.md"
    fi
}

# 主程序
main() {
    # 檢查參數
    if [ $# -lt 1 ]; then
        show_usage
        exit 1
    fi
    
    EXAMPLE_NAME="$1"
    ACTION="${2:-apply}"  # 默認動作是 apply
    
    # 特殊處理: help 或 --help
    if [[ "$EXAMPLE_NAME" =~ ^(help|--help|-h)$ ]]; then
        show_usage
        exit 0
    fi
    
    # 獲取範例路徑
    EXAMPLE_PATH=$(get_example_path "$EXAMPLE_NAME")
    
    if [ -z "$EXAMPLE_PATH" ]; then
        echo "❌ 錯誤: 未知的範例名稱 '$EXAMPLE_NAME'"
        echo ""
        echo "💡 可用的範例名稱:"
        echo "   comprehensive-dev, comprehensive-sit"
        echo "   team-skilltree, team-sub1, team-sub2"
        echo "   images-override"
        echo "   patch-inline, patch-strategic, patch-json"
        echo "   complex-composition"
        echo ""
        echo "🔍 使用 '$0 help' 查看詳細說明"
        exit 1
    fi
    
    # 檢查目錄是否存在
    if [ ! -d "$SCRIPT_DIR/$EXAMPLE_PATH" ]; then
        echo "❌ 錯誤: 範例目錄不存在: $SCRIPT_DIR/$EXAMPLE_PATH"
        echo "🔍 請檢查目錄結構是否正確"
        exit 1
    fi
    
    # 檢查依賴
    check_dependencies
    
    # 執行動作
    execute_action "$EXAMPLE_PATH" "$ACTION" "$EXAMPLE_NAME"
    
    # 顯示學習建議
    if [ "$ACTION" = "build" ] || [ "$ACTION" = "apply" ]; then
        show_learning_tips "$EXAMPLE_NAME"
    fi
}

# 如果腳本被直接執行，則運行主程序
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
