apiVersion: apps/v1
kind: Deployment
metadata:
  name: batchsystem-multi-service
  labels:
    app: batchsystem-multi
spec:
  replicas: 1
  selector:
    matchLabels:
      app: batchsystem-multi
  template:
    metadata:
      labels:
        app: batchsystem-multi
    spec:
      containers:
      - name: api
        image: nexus-container-registry.jkopay.app/found/jkopay.batchsystem.api:latest
        ports:
        - containerPort: 8080
      - name: job
        image: nexus-container-registry.jkopay.app/found/jkopay.batchsystem.job:latest
        ports:
        - containerPort: 8090
      - name: operator
        image: nexus-container-registry.jkopay.app/found/jkopay.batchsystem.operator:latest
        ports:
        - containerPort: 8100
