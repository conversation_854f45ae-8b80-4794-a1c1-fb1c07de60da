#!/bin/bash

# Kustomize 範例驗證腳本 (合併優化版)
# 驗證所有範例的語法正確性和邏輯一致性

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 顏色輸出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 統計變數
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 輸出帶顏色的訊息
print_status() {
    local status="$1"
    local message="$2"
    
    case "$status" in
        "INFO")
            echo -e "${BLUE}ℹ️  $message${NC}"
            ;;
        "SUCCESS")
            echo -e "${GREEN}✅ $message${NC}"
            ((PASSED_TESTS++))
            ;;
        "WARNING")
            echo -e "${YELLOW}⚠️  $message${NC}"
            ;;
        "ERROR")
            echo -e "${RED}❌ $message${NC}"
            ((FAILED_TESTS++))
            ;;
    esac
    ((TOTAL_TESTS++))
}

# 檢查必要的工具
check_dependencies() {
    print_status "INFO" "檢查依賴工具..."
    
    local missing_tools=()
    
    if ! command -v kustomize &> /dev/null; then
        missing_tools+=("kustomize")
    fi
    
    if ! command -v kubectl &> /dev/null; then
        missing_tools+=("kubectl")
    fi
    
    if [ ${#missing_tools[@]} -ne 0 ]; then
        print_status "ERROR" "缺少必要工具: ${missing_tools[*]}"
        echo ""
        echo "安裝建議:"
        echo "  kustomize: https://kubectl.docs.kubernetes.io/installation/kustomize/"
        echo "  kubectl: https://kubernetes.io/docs/tasks/tools/"
        exit 1
    fi
    
    print_status "SUCCESS" "所有依賴工具已安裝"
}

# 驗證目錄結構
validate_directory_structure() {
    print_status "INFO" "驗證目錄結構..."
    
    local expected_dirs=(
        "01-comprehensive-base-overlays"
        "02-images-override"
        "03-patch-techniques"
        "04-complex-composition"
    )
    
    for dir in "${expected_dirs[@]}"; do
        if [ -d "$SCRIPT_DIR/$dir" ]; then
            print_status "SUCCESS" "目錄存在: $dir"
        else
            print_status "ERROR" "目錄不存在: $dir"
        fi
    done
}

# 驗證單個範例
validate_example() {
    local path="$1"
    local name="$2"
    local full_path="$SCRIPT_DIR/$path"
    
    if [ ! -d "$full_path" ]; then
        print_status "ERROR" "$name: 目錄不存在 ($path)"
        return 1
    fi
    
    if [ ! -f "$full_path/kustomization.yaml" ]; then
        print_status "ERROR" "$name: 缺少 kustomization.yaml"
        return 1
    fi
    
    # 執行 kustomize build 驗證
    if kustomize build "$full_path" > /dev/null 2>&1; then
        print_status "SUCCESS" "$name: kustomize build 成功"
    else
        print_status "ERROR" "$name: kustomize build 失敗"
        echo "詳細錯誤:"
        kustomize build "$full_path" 2>&1 | sed 's/^/  /'
        return 1
    fi
    
    # 執行 kubectl dry-run 驗證
    if kustomize build "$full_path" | kubectl apply --dry-run=client -f - > /dev/null 2>&1; then
        print_status "SUCCESS" "$name: kubectl dry-run 成功"
    else
        print_status "ERROR" "$name: kubectl dry-run 失敗"
        echo "詳細錯誤:"
        kustomize build "$full_path" | kubectl apply --dry-run=client -f - 2>&1 | sed 's/^/  /'
        return 1
    fi
    
    return 0
}

# 驗證所有綜合範例
validate_comprehensive_examples() {
    print_status "INFO" "驗證綜合 Base/Overlays 範例..."
    
    local examples=(
        "01-comprehensive-base-overlays/overlays/dev:綜合範例-開發環境"
        "01-comprehensive-base-overlays/overlays/sit:綜合範例-SIT 環境"
        "01-comprehensive-base-overlays/teams/skilltree:skilltree 團隊配置"
        "01-comprehensive-base-overlays/teams/sub1:sub1 團隊配置"
        "01-comprehensive-base-overlays/teams/sub2:sub2 團隊配置"
    )
    
    for example in "${examples[@]}"; do
        IFS=':' read -r path name <<< "$example"
        validate_example "$path" "$name"
    done
}

# 驗證 Patch 技巧範例
validate_patch_examples() {
    print_status "INFO" "驗證 Patch 技巧範例..."
    
    local examples=(
        "03-patch-techniques/inline-patches:Inline Patches"
        "03-patch-techniques/strategic-merge:Strategic Merge Patches"
        "03-patch-techniques/json-patches:JSON Patches"
    )
    
    for example in "${examples[@]}"; do
        IFS=':' read -r path name <<< "$example"
        validate_example "$path" "$name"
    done
}

# 驗證其他範例
validate_other_examples() {
    print_status "INFO" "驗證其他範例..."
    
    local examples=(
        "02-images-override:Images 覆寫"
        "04-complex-composition/environments/production:複雜組合配置"
    )
    
    for example in "${examples[@]}"; do
        IFS=':' read -r path name <<< "$example"
        validate_example "$path" "$name"
    done
}

# 驗證文檔文件
validate_documentation() {
    print_status "INFO" "驗證文檔文件..."
    
    local docs=(
        "README.md:主要說明文檔"
        "ARCHITECTURE.md:架構說明文檔"
        "03-patch-techniques/README.md:Patch 技巧說明"
    )
    
    for doc in "${docs[@]}"; do
        IFS=':' read -r file desc <<< "$doc"
        if [ -f "$SCRIPT_DIR/$file" ]; then
            print_status "SUCCESS" "文檔存在: $desc"
        else
            print_status "ERROR" "文檔缺失: $desc ($file)"
        fi
    done
}

# 驗證腳本文件
validate_scripts() {
    print_status "INFO" "驗證腳本文件..."
    
    local scripts=(
        "deploy.sh:部署腳本"
        "validate.sh:驗證腳本 (自己)"
    )
    
    for script in "${scripts[@]}"; do
        IFS=':' read -r file desc <<< "$script"
        if [ -f "$SCRIPT_DIR/$file" ]; then
            if [ -x "$SCRIPT_DIR/$file" ]; then
                print_status "SUCCESS" "腳本可執行: $desc"
            else
                print_status "WARNING" "腳本不可執行: $desc (建議 chmod +x $file)"
            fi
        else
            print_status "ERROR" "腳本缺失: $desc ($file)"
        fi
    done
}

# 執行全面的資源檢查
validate_resource_consistency() {
    print_status "INFO" "檢查資源一致性..."
    
    # 檢查是否有孤立的資源文件
    local yaml_files=$(find "$SCRIPT_DIR" -name "*.yaml" -not -path "*/kafka/*" | wc -l)
    local kustomization_files=$(find "$SCRIPT_DIR" -name "kustomization.yaml" | wc -l)
    
    print_status "INFO" "發現 $yaml_files 個 YAML 檔案，$kustomization_files 個 kustomization.yaml"
    
    # 檢查是否有重複的資源名稱
    local temp_file="/tmp/kustomize_validation_$$.txt"
    
    for example_dir in "$SCRIPT_DIR"/*/; do
        if [ -f "$example_dir/kustomization.yaml" ]; then
            local dir_name=$(basename "$example_dir")
            if kustomize build "$example_dir" > "$temp_file" 2>/dev/null; then
                local resource_count=$(grep -c "^kind:" "$temp_file" 2>/dev/null || echo 0)
                print_status "SUCCESS" "$dir_name: 生成 $resource_count 個 Kubernetes 資源"
            fi
        fi
    done
    
    rm -f "$temp_file"
}

# 顯示詳細的統計報告
show_summary() {
    echo ""
    echo "========================================"
    echo "📊 驗證結果總結"
    echo "========================================"
    echo "總測試數:   $TOTAL_TESTS"
    echo "成功測試:   $PASSED_TESTS"
    echo "失敗測試:   $FAILED_TESTS"
    echo "成功率:     $(( PASSED_TESTS * 100 / TOTAL_TESTS ))%"
    echo "========================================"
    
    if [ $FAILED_TESTS -eq 0 ]; then
        print_status "SUCCESS" "🎉 所有驗證都通過了！"
        echo ""
        echo "💡 下一步建議:"
        echo "   - 執行 './deploy.sh comprehensive-dev build' 測試部署"
        echo "   - 閱讀 README.md 了解使用方法"
        echo "   - 按照學習路徑循序漸進"
    else
        print_status "ERROR" "❌ 有 $FAILED_TESTS 個測試失敗"
        echo ""
        echo "🔍 修復建議:"
        echo "   - 檢查失敗的範例配置"
        echo "   - 確保 kustomization.yaml 語法正確"
        echo "   - 確認所有引用的資源文件存在"
        exit 1
    fi
}

# 執行特定範例的驗證
validate_specific_example() {
    local example_name="$1"
    
    print_status "INFO" "驗證特定範例: $example_name"
    
    case "$example_name" in
        "comprehensive-dev")
            validate_example "01-comprehensive-base-overlays/overlays/dev" "綜合範例-開發環境"
            ;;
        "comprehensive-sit")
            validate_example "01-comprehensive-base-overlays/overlays/sit" "綜合範例-SIT 環境"
            ;;
        "team-skilltree")
            validate_example "01-comprehensive-base-overlays/teams/skilltree" "skilltree 團隊配置"
            ;;
        "team-sub1")
            validate_example "01-comprehensive-base-overlays/teams/sub1" "sub1 團隊配置"
            ;;
        "team-sub2")
            validate_example "01-comprehensive-base-overlays/teams/sub2" "sub2 團隊配置"
            ;;
        # 角色配置已移除
        "images-override")
            validate_example "02-images-override" "Images 覆寫"
            ;;
        "patch-inline")
            validate_example "03-patch-techniques/inline-patches" "Inline Patches"
            ;;
        "patch-strategic")
            validate_example "03-patch-techniques/strategic-merge" "Strategic Merge Patches"
            ;;
        "patch-json")
            validate_example "03-patch-techniques/json-patches" "JSON Patches"
            ;;
        "complex-composition")
            validate_example "04-complex-composition/environments/production" "複雜組合配置"
            ;;
        *)
            print_status "ERROR" "未知的範例名稱: $example_name"
            echo ""
            echo "可用的範例名稱:"
            echo "  comprehensive-dev, comprehensive-sit"
            echo "  team-skilltree, team-sub1, team-sub2"
            echo "  images-override"
            echo "  patch-inline, patch-strategic, patch-json"
            echo "  complex-composition"
            exit 1
            ;;
    esac
}

# 顯示使用說明
show_usage() {
    echo "用法: $0 [選項] [範例名稱]"
    echo ""
    echo "選項:"
    echo "  -h, --help     顯示此說明"
    echo "  -v, --verbose  詳細輸出"
    echo "  -q, --quiet    安靜模式（最少輸出）"
    echo ""
    echo "範例名稱:"
    echo "  不指定 - 驗證所有範例"
    echo "  comprehensive-dev, comprehensive-sit"
    echo "  team-skilltree, team-sub1, team-sub2"
    echo "  images-override"
    echo "  patch-inline, patch-strategic, patch-json"
    echo "  complex-composition"
    echo ""
    echo "範例:"
    echo "  $0                    # 驗證所有範例"
    echo "  $0 comprehensive-dev  # 只驗證綜合範例開發環境"
    echo "  $0 --verbose          # 詳細輸出驗證所有範例"
}

# 主函數
main() {
    # 處理命令行參數
    local verbose=false
    local quiet=false
    local specific_example=""
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_usage
                exit 0
                ;;
            -v|--verbose)
                verbose=true
                shift
                ;;
            -q|--quiet)
                quiet=true
                shift
                ;;
            *)
                if [ -z "$specific_example" ]; then
                    specific_example="$1"
                else
                    echo "錯誤: 只能指定一個範例名稱"
                    show_usage
                    exit 1
                fi
                shift
                ;;
        esac
    done
    
    if [ "$quiet" = false ]; then
        echo "🔍 Kustomize 範例驗證器 (合併優化版)"
        echo "========================================"
        echo "開始驗證時間: $(date)"
        echo ""
    fi
    
    # 重設統計
    TOTAL_TESTS=0
    PASSED_TESTS=0
    FAILED_TESTS=0
    
    # 執行驗證
    if [ -n "$specific_example" ]; then
        # 驗證特定範例
        check_dependencies
        validate_specific_example "$specific_example"
    else
        # 驗證所有範例
        check_dependencies
        validate_directory_structure
        validate_comprehensive_examples
        validate_patch_examples
        validate_other_examples
        validate_documentation
        validate_scripts
        
        if [ "$verbose" = true ]; then
            validate_resource_consistency
        fi
    fi
    
    # 顯示結果
    if [ "$quiet" = false ]; then
        show_summary
    else
        if [ $FAILED_TESTS -eq 0 ]; then
            echo "OK"
        else
            echo "FAILED"
            exit 1
        fi
    fi
}

# 如果腳本被直接執行，則運行主程序
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
