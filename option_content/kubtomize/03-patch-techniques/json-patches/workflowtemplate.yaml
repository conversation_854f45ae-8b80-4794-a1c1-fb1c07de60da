apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: batchsystem-main-cron-workflow-template
  labels:
    app: batchsystem-cron
spec:
  serviceAccountName: batchsystem-sa
  entrypoint: cron-main
  arguments:
    parameters:
    - name: database-url
      value: "original-database-url"
    - name: api-key
      value: "original-api-key"
    - name: batch-size
      value: "100"
    - name: timeout-seconds
      value: "300"
    - name: retry-attempts
      value: "3"
    - name: service-endpoint
      value: "original-batchsystem-api-svc:9090"
    - name: log-level
      value: "info"
  templates:
  - name: cron-main
    container:
      image: nexus-container-registry.jkopay.app/found/jkopay.batchsystem.cron:latest
      command: ["/app/cron-processor"]
      args:
      - "--database-url={{workflow.parameters.database-url}}"
      - "--api-key={{workflow.parameters.api-key}}"
      - "--batch-size={{workflow.parameters.batch-size}}"
      - "--timeout={{workflow.parameters.timeout-seconds}}"
      - "--retry={{workflow.parameters.retry-attempts}}"
      - "--endpoint={{workflow.parameters.service-endpoint}}"
      - "--log-level={{workflow.parameters.log-level}}"
      resources:
        requests:
          memory: 256Mi
          cpu: 100m
        limits:
          memory: 512Mi
          cpu: 200m
