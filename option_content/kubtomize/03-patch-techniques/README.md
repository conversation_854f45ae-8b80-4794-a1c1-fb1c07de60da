# Patch 技巧範例集合

本目錄展示三種主要的 Kustomize patch 技巧：

## 目錄結構

```
03-patch-techniques/
├── inline-patches/       # Inline JSON Patches
├── strategic-merge/      # Strategic Merge Patches
└── json-patches/         # 精準 JSON Patch 路徑修改
```

## 1. Inline Patches (inline-patches/)

**用途：** 直接在 kustomization.yaml 中定義小量的 JSON Patch 變更

**優點：**
- 不需要額外的 patch 文件
- 適合簡單的配置變更
- 一目了然，容易維護

**範例：**
```yaml
patches:
  - target:
      kind: EventSource
      name: kafka-eventsource
    patch: |-
      - op: replace
        path: /spec/kafka/priority/url
        value: kafka-bootstrap.production.svc.cluster.local:9092
```

**執行命令：**
```bash
kustomize build inline-patches/
```

## 2. Strategic Merge Patches (strategic-merge/)

**用途：** 使用 YAML 文件進行複雜的配置合併

**優點：**
- 適合大量配置變更
- 可以添加新的配置段落
- YAML 格式易於閱讀和編輯

**範例：**
```yaml
patchesStrategicMerge:
  - deployment-patch.yaml
```

**執行命令：**
```bash
kustomize build strategic-merge/
```

## 3. JSON Patches (json-patches/)

**用途：** 精確的路徑級別配置修改

**優點：**
- 精確控制修改位置
- 支持複雜的陣列操作
- 適合精確的參數調整

**範例：**
```yaml
patches:
  - target:
      kind: WorkflowTemplate
      name: batchsystem-main-cron-workflow-template
    patch: |-
      - op: replace
        path: /spec/arguments/parameters/0/value
        value: updated-database-url
      - op: add
        path: /spec/arguments/parameters/-
        value:
          name: new-parameter
          value: new-value
```

**執行命令：**
```bash
kustomize build json-patches/
```

## 使用建議

### 何時使用 Inline Patches？
- 簡單的值替換（如替換 image tag、調整副本數）
- 小量的配置變更（少於 10 行）
- 不需要複用的一次性變更

### 何時使用 Strategic Merge Patches？
- 需要添加新的配置段落
- 複雜的配置變更（如 Vault 注入）
- 需要在多個地方複用相同的 patch

### 何時使用 JSON Patches？
- 需要精確控制修改位置
- 操作陣列元素（如添加、刪除特定位置的元素）
- 複雜的條件性變更

## 最佳實踐

1. **選擇適合的 Patch 類型**：根據變更的複雜度選擇
2. **保持 Patch 的簡潔**：每個 patch 只做一件事
3. **添加註釋**：說明 patch 的目的和影響
4. **測試 Patch**：使用 `kustomize build` 驗證結果
5. **版本控制**：將 patch 文件納入版本控制

## 測試所有 Patch 範例

```bash
# 測試所有 patch 技巧
for dir in inline-patches strategic-merge json-patches; do
    echo "Testing $dir..."
    kustomize build 03-patch-techniques/$dir/
    echo "---"
done
```
