apiVersion: argoproj.io/v1alpha1
kind: WorkflowTemplate
metadata:
  name: batchsystem-workflow-template
  labels:
    app: batchsystem
spec:
  serviceAccountName: batchsystem-sa
  entrypoint: main
  arguments:
    parameters:
    - name: team-name
      value: "default"
    - name: resource-quota
      value: "standard"
  templates:
  - name: main
    container:
      image: nexus-container-registry.jkopay.app/found/jkopay.batchsystem.job:latest
      command: ["/app/processor"]
      args:
      - "--team={{workflow.parameters.team-name}}"
      - "--quota={{workflow.parameters.resource-quota}}"
      resources:
        requests:
          memory: 128Mi
          cpu: 50m
        limits:
          memory: 256Mi
          cpu: 100m
