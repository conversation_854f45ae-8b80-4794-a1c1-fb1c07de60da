apiVersion: v1
kind: ServiceAccount
metadata:
  name: batchsystem-sa
  labels:
    app: batchsystem
    team: default
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: batchsystem-role
  labels:
    app: batchsystem
rules:
- apiGroups: [""]
  resources: ["pods", "pods/log", "configmaps", "secrets"]
  verbs: ["get", "list", "create", "delete"]
- apiGroups: ["argoproj.io"]
  resources: ["workflows", "workflowtemplates"]
  verbs: ["get", "list", "create", "delete", "patch", "update"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: batchsystem-binding
  labels:
    app: batchsystem
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: batchsystem-role
subjects:
- kind: ServiceAccount
  name: batchsystem-sa
  namespace: default
