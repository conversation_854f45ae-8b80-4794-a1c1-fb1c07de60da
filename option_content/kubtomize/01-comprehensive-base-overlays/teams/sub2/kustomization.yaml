apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
  - ../../base

namespace: sub2
namePrefix: sub2-

commonLabels:
  team: sub2

patches:
  # 替換 ServiceAccount 配置
  - target:
      kind: ServiceAccount
      name: batchsystem-sa
    patch: |-
      - op: replace
        path: /metadata/labels/team
        value: sub2
      - op: add
        path: /metadata/annotations
        value:
          description: "ServiceAccount for sub2 team"
  
  # 更新 WorkflowTemplate 的參數
  - target:
      kind: WorkflowTemplate
      name: batchsystem-workflow-template
    patch: |-
      - op: replace
        path: /spec/arguments/parameters/0/value
        value: sub2
      - op: replace
        path: /spec/arguments/parameters/1/value
        value: standard
