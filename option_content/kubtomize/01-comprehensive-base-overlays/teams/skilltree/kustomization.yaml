apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
  - ../../base

namespace: skilltree
namePrefix: skilltree-

commonLabels:
  team: skilltree

patches:
  # 替換 ServiceAccount 配置
  - target:
      kind: ServiceAccount
      name: batchsystem-sa
    patch: |-
      - op: replace
        path: /metadata/labels/team
        value: skilltree
      - op: add
        path: /metadata/annotations
        value:
          description: "ServiceAccount for skilltree team"
  
  # 更新 WorkflowTemplate 的參數
  - target:
      kind: WorkflowTemplate
      name: batchsystem-workflow-template
    patch: |-
      - op: replace
        path: /spec/arguments/parameters/0/value
        value: skilltree
      - op: replace
        path: /spec/arguments/parameters/1/value
        value: premium
      - op: replace
        path: /spec/templates/0/container/resources
        value:
          requests:
            memory: 256Mi
            cpu: 100m
          limits:
            memory: 512Mi
            cpu: 200m
