apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
  - ../../base

namespace: sub1
namePrefix: sub1-

commonLabels:
  team: sub1

patches:
  # 替換 ServiceAccount 配置
  - target:
      kind: ServiceAccount
      name: batchsystem-sa
    patch: |-
      - op: replace
        path: /metadata/labels/team
        value: sub1
      - op: add
        path: /metadata/annotations
        value:
          description: "ServiceAccount for sub1 team"
  
  # 更新 WorkflowTemplate 的參數
  - target:
      kind: WorkflowTemplate
      name: batchsystem-workflow-template
    patch: |-
      - op: replace
        path: /spec/arguments/parameters/0/value
        value: sub1
      - op: replace
        path: /spec/arguments/parameters/1/value
        value: standard
