apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

resources:
  - ../../base

namePrefix: dev-
namespace: dev

labels:
- pairs:
    environment: dev

patches:
  # 調整 Deployment 副本數
  - patch: |-
      - op: replace
        path: /spec/replicas
        value: 1
    target:
      kind: Deployment
      name: batchsystem-api
  
  # 調整資源限制
  - patch: |-
      - op: replace
        path: /spec/templates/0/container/resources
        value:
          requests:
            memory: 64Mi
            cpu: 25m
          limits:
            memory: 128Mi
            cpu: 50m
    target:
      kind: WorkflowTemplate
      name: batchsystem-workflow-template
