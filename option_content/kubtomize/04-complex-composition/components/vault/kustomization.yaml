apiVersion: kustomize.config.k8s.io/v1alpha1
kind: Component

patches:
  - target:
      kind: Deployment
      name: batchsystem-app
    patch: |-
      - op: add
        path: /spec/template/metadata/annotations
        value:
          vault.hashicorp.com/agent-inject: "true"
          vault.hashicorp.com/role: "batchsystem"
          vault.hashicorp.com/agent-inject-secret-database: "secrets/data/batchsystem/database"
          vault.hashicorp.com/agent-inject-template-database: |
            {{- with secret "secrets/data/batchsystem/database" -}}
            export DATABASE_URL="{{ .Data.data.url }}"
            export DATABASE_USER="{{ .Data.data.username }}"
            export DATABASE_PASS="{{ .Data.data.password }}"
            {{- end -}}
      - op: add
        path: /spec/template/spec/containers/0/command
        value: ["/bin/sh", "-c"]
      - op: add
        path: /spec/template/spec/containers/0/args
        value: ["source /vault/secrets/database && exec /app/start.sh"]

resources:
  - serviceaccount.yaml
