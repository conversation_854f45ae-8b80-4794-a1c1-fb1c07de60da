apiVersion: kustomize.config.k8s.io/v1alpha1
kind: Component

patches:
  - target:
      kind: Deployment
      name: batchsystem-app
    patch: |-
      - op: add
        path: /spec/template/metadata/annotations/prometheus.io~1scrape
        value: "true"
      - op: add
        path: /spec/template/metadata/annotations/prometheus.io~1port
        value: "9090"
      - op: add
        path: /spec/template/metadata/annotations/prometheus.io~1path
        value: "/metrics"
      - op: add
        path: /spec/template/spec/containers/0/ports/-
        value:
          name: metrics
          containerPort: 9090
          protocol: TCP

resources:
  - servicemonitor.yaml
