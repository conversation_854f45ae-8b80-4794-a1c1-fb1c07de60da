apiVersion: apps/v1
kind: Deployment
metadata:
  name: batchsystem-app
  labels:
    app: batchsystem
spec:
  replicas: 1
  selector:
    matchLabels:
      app: batchsystem
  template:
    metadata:
      labels:
        app: batchsystem
    spec:
      containers:
      - name: app
        image: nexus-container-registry.jkopay.app/found/jkopay.batchsystem.api:latest
        ports:
        - containerPort: 8080
        env:
        - name: APP_ENV
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: APP_ENV
        - name: LOG_LEVEL
          valueFrom:
            configMapKeyRef:
              name: app-config
              key: LOG_LEVEL
        resources:
          requests:
            memory: 256Mi
            cpu: 100m
          limits:
            memory: 512Mi
            cpu: 200m
