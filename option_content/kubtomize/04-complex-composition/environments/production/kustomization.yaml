apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namePrefix: prod-
namespace: production

resources:
  - ../../base

components:
  - ../../components/vault
  - ../../components/monitoring

images:
  - name: nexus-container-registry.jkopay.app/found/jkopay.batchsystem.api
    newTag: 1.0.0

configMapGenerator:
  - name: app-config
    behavior: merge
    literals:
      - APP_ENV=production
      - LOG_LEVEL=warn
      - WORKERS=4
      - CACHE_SIZE=1000

patches:
  - target:
      kind: Deployment
      name: batchsystem-app
    patch: |-
      - op: replace
        path: /spec/replicas
        value: 3
      - op: replace
        path: /spec/template/spec/containers/0/resources
        value:
          requests:
            memory: 1Gi
            cpu: 500m
          limits:
            memory: 2Gi
            cpu: 1000m
      - op: add
        path: /spec/template/spec/containers/0/env/-
        value:
          name: ENVIRONMENT
          value: production
  
  - target:
      kind: Service
      name: batchsystem-service
    patch: |-
      - op: add
        path: /metadata/annotations
        value:
          service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
          service.beta.kubernetes.io/aws-load-balancer-internal: "true"

replicas:
  - name: batchsystem-app
    count: 3

commonLabels:
  environment: production
  tier: backend

commonAnnotations:
  deployment.kubernetes.io/revision-history-limit: "5"
