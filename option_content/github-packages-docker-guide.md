# 將 Docker Image 上傳至 GitHub Packages 教學

本教學將引導你完成將 Docker image 上傳至 GitHub Packages 的完整流程，包含 Docker 登入的部分。

## 前置條件

- 已安裝 Docker
- 擁有 GitHub 帳號
- 已經準備好要上傳的 Docker image (或將在本教學中創建一個簡單的範例)

## 1. 創建 GitHub 個人存取令牌 (Personal Access Token)

首先，你需要一個 GitHub 個人存取令牌來進行身份驗證：

1. 登入你的 GitHub 帳號
2. 點擊右上角頭像 → Settings → Developer settings → Personal access tokens → Tokens (classic) → Generate new token
3. 提供一個有意義的描述，例如「Docker GitHub Packages」
4. 選擇令牌的有效期限
5. 勾選以下權限：
   - `repo` (所有子權限)
   - `write:packages`
   - `read:packages`
   - `delete:packages`
6. 點擊「Generate token」，並複製生成的令牌（請妥善保管，此令牌僅顯示一次）

## 2. Docker 登入至 GitHub Packages

使用你的 GitHub 帳號和剛剛生成的個人存取令牌登入 GitHub Packages 的 Docker registry：

```bash
# GitHub Packages Docker registry 的格式
# docker login ghcr.io -u YOUR_GITHUB_USERNAME -p YOUR_GITHUB_TOKEN

# 例如：
docker login ghcr.io -u username -p ghp_abcdefghijklmnopqrstuvwxyz123456
```

最佳實踐提示：為避免在命令列中直接使用令牌（會記錄在歷史紀錄中），你可以改用以下方式：

```bash
# 通過 stdin 傳入密碼
echo $GITHUB_TOKEN | docker login ghcr.io -u username --password-stdin
```

## 3. 為你的 Docker image 建立標籤 (Tag)

為了上傳至 GitHub Packages，你的 image 需要遵循特定的命名規則：

```bash
# 格式：ghcr.io/GITHUB_USERNAME/IMAGE_NAME:TAG

# 假設你已經有一個本地 image 叫 myapp:latest
docker tag myapp:latest ghcr.io/username/myapp:1.0.0
```

如果你還沒有 Docker image，可以使用以下簡單的 Dockerfile 建立一個：

```dockerfile
FROM nginx:alpine
LABEL org.opencontainers.image.source=https://github.com/username/repo-name
```

然後建立並標記該 image：

```bash
# 建立 image
docker build -t myapp .

# 標記 image
docker tag myapp ghcr.io/username/myapp:1.0.0
```

注意：`org.opencontainers.image.source` 標籤可以幫助連結 image 和對應的 GitHub 儲存庫。

## 4. 推送 (Push) Docker image 至 GitHub Packages

完成標記後，推送 image 至 GitHub Packages：

```bash
docker push ghcr.io/username/myapp:1.0.0
```

## 5. 設定 Package 可見性

預設情況下，推送的 package 是私有的。如果你想要讓它公開可見：

1. 前往 GitHub 網站中的 Package 頁面
2. 選擇你上傳的 package
3. 點擊 "Package settings"
4. 在 "Danger Zone" 區域，找到 "Change visibility" 選項
5. 選擇 "Public" 並確認

## 6. 從 GitHub Packages 拉取 (Pull) image

如果需要從 GitHub Packages 拉取 image，可以使用以下命令：

```bash
# 如果 package 是私有的，需要先登入
docker login ghcr.io -u username -p YOUR_GITHUB_TOKEN

# 拉取 image
docker pull ghcr.io/username/myapp:1.0.0
```

## 常見問題解決

### 權限問題

如果遇到 `denied: permission_denied` 錯誤，請確認：
- 個人存取令牌具有正確的權限
- 你擁有對應儲存庫的存取權
