# Nexus 與 Containerd 設定指南

## 調整 containerd 設定 (只有需要抓取 http 的 private image registry 需要設定)

Containerd 是一個容器執行時，用於管理容器的生命週期。

### 安裝 vim，方便後續修改 toml

```bash
sudo apt update && sudo apt install -y vim
vim /etc/containerd/config.toml
```

修改 `/etc/containerd/config.toml`，設定 Containerd 信任 Nexus 倉庫。

```toml
[plugins."io.containerd.grpc.v1.cri".registry]
  config_path = "/etc/containerd/certs.d"

[plugins."io.containerd.grpc.v1.cri".registry.configs."nexus3.lab.svc.cluster.local:8082".tls]
  insecure_skip_verify = true
```

* `config_path`: 指定存放憑證的目錄。
* `insecure_skip_verify`: 允許 Containerd 忽略 Nexus 倉庫的 SSL 憑證驗證。

建立 `/etc/containerd/certs.d/nexus3.lab.svc.cluster.local:8082/` 目錄，並建立 `hosts.toml` 檔案。

```bash
mkdir /etc/containerd/certs.d/nexus3.lab.svc.cluster.local:8082/
vim hosts.toml
```
> 這裡的 8082 是因為 private image registry 使用的 port

`hosts.toml` 範例：

```toml
server = "http://nexus3.lab.svc.cluster.local:8082"

[host."http://nexus3.lab.svc.cluster.local:8082"]
  capabilities = ["pull", "resolve"]
  skip_verify = true
```

* `server`: 指定 Nexus 倉庫的 URL。
* `capabilities`: 指定可以從 Nexus 倉庫 pull 和 resolve 映像檔。
* `skip_verify`: 允許 Containerd 忽略 Nexus 倉庫的 SSL 憑證驗證。

## 安裝 Nexus

Nexus 是可以用於儲存和管理您的 Docker 映像檔和其他依賴項。

### 為什麼需要自建一個 Image Registry

K8s 只能夠從 Image Registry 上拉取 Image 來進行佈署，所以當在 K8s 上需要佈署 Pod 時，需要將 Image 上傳到 Image Registry 供佈署使用。

### Nexus 登入

預設使用者名稱為 `admin`。

取得密碼：

```bash
kubectl -n lab exec {pod name} -it cat /nexus-data/admin.password
```

請將 `{pod name}` 替換為 Nexus Pod 的名稱。

建立 `nexus3-secret`，用於 Docker 登入：

```bash
kubectl create secret docker-registry nexus3-secret \
  --docker-server=nexus3.lab.svc.cluster.local:8082 \
  --docker-username=admin \
  --docker-password=admin